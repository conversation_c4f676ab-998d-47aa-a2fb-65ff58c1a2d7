#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - 快捷方式管理器
负责创建和管理Windows快捷方式，支持自定义图标

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import os
import sys
import logging
from pathlib import Path
from typing import Optional, Dict, Any
import subprocess

# Windows COM组件支持
try:
    import win32com.client
    import pythoncom
    COM_AVAILABLE = True
except ImportError:
    COM_AVAILABLE = False
    print("⚠️ pywin32未安装，快捷方式功能将受限")

# 导入配置管理器
try:
    from 配置管理器 import config_manager, get_config
except ImportError:
    print("❌ 无法导入配置管理器，请确保配置管理器.py文件存在")
    sys.exit(1)

class ShortcutManager:
    """快捷方式管理器 - 创建和管理Windows快捷方式"""

    def __init__(self):
        """初始化快捷方式管理器"""
        self.config = config_manager
        self.project_root = self.config.get_project_root()
        self.browsers_dir = self.config.get_browsers_dir()

        self._setup_logging()
        self._check_com_support()

        self.logger.info("快捷方式管理器初始化完成")

    def _setup_logging(self):
        """设置日志系统"""
        log_dir = self.project_root / "日志"
        log_dir.mkdir(exist_ok=True)

        self.logger = logging.getLogger("ShortcutManager")
        if not self.logger.handlers:
            handler = logging.FileHandler(log_dir / "快捷方式管理器.log", encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

    def _check_com_support(self):
        """检查COM组件支持"""
        if not COM_AVAILABLE:
            self.logger.warning("pywin32未安装，快捷方式功能将使用备用方案")
        else:
            self.logger.info("COM组件支持正常")

    def create_desktop_shortcut(self, browser_name: str, custom_icon: Optional[str] = None) -> bool:
        """
        创建桌面快捷方式

        Args:
            browser_name: 浏览器名称
            custom_icon: 自定义图标路径（可选）

        Returns:
            是否创建成功
        """
        try:
            browser_dir = self.browsers_dir / browser_name
            if not browser_dir.exists():
                self.logger.error(f"浏览器 {browser_name} 不存在")
                return False

            # 获取桌面路径
            desktop_path = self._get_desktop_path()
            if not desktop_path:
                return False

            # 快捷方式文件路径
            shortcut_path = desktop_path / f"{browser_name}.lnk"

            # 目标程序路径
            target_path = browser_dir / "Chrome-bin" / "GoogleChromePortable.exe"
            if not target_path.exists():
                self.logger.error(f"目标程序不存在: {target_path}")
                return False

            # 工作目录
            working_dir = str(browser_dir)

            # 启动参数（优化，移除安全风险参数）
            arguments = f'--user-data-dir="Data_{browser_name}" --no-default-browser-check --disable-default-apps --disable-background-timer-throttling'

            # 图标路径
            icon_path = custom_icon or self._find_browser_icon(browser_dir)

            # 创建快捷方式
            if COM_AVAILABLE:
                success = self._create_shortcut_com(
                    str(shortcut_path), str(target_path), working_dir,
                    arguments, icon_path, browser_name
                )
            else:
                success = self._create_shortcut_fallback(
                    str(shortcut_path), str(target_path), working_dir,
                    arguments, browser_name
                )

            if success:
                self.logger.info(f"桌面快捷方式创建成功: {shortcut_path}")
                return True
            else:
                self.logger.error("快捷方式创建失败")
                return False

        except Exception as e:
            self.logger.error(f"创建桌面快捷方式失败: {e}")
            return False

    def _get_desktop_path(self) -> Optional[Path]:
        """获取桌面路径"""
        try:
            # Windows桌面路径
            desktop = Path.home() / "Desktop"
            if desktop.exists():
                return desktop

            # 中文桌面路径
            desktop_cn = Path.home() / "桌面"
            if desktop_cn.exists():
                return desktop_cn

            # 使用环境变量
            desktop_env = os.environ.get("USERPROFILE")
            if desktop_env:
                desktop_path = Path(desktop_env) / "Desktop"
                if desktop_path.exists():
                    return desktop_path

            self.logger.error("无法找到桌面路径")
            return None

        except Exception as e:
            self.logger.error(f"获取桌面路径失败: {e}")
            return None

    def _find_browser_icon(self, browser_dir: Path) -> Optional[str]:
        """查找浏览器图标"""
        try:
            # 支持的图标格式
            icon_extensions = ['.ico', '.png', '.jpg', '.jpeg']

            for ext in icon_extensions:
                for icon_file in browser_dir.glob(f"*{ext}"):
                    return str(icon_file)

            # 使用默认图标
            default_icon = self.config.get_icons_dir() / "generic.png"
            if default_icon.exists():
                return str(default_icon)

            return None

        except Exception as e:
            self.logger.error(f"查找浏览器图标失败: {e}")
            return None

    def _create_shortcut_com(self, shortcut_path: str, target_path: str,
                            working_dir: str, arguments: str,
                            icon_path: Optional[str], description: str) -> bool:
        """使用COM组件创建快捷方式"""
        try:
            # 初始化COM
            pythoncom.CoInitialize()

            # 创建Shell对象
            shell = win32com.client.Dispatch("WScript.Shell")

            # 创建快捷方式对象
            shortcut = shell.CreateShortCut(shortcut_path)

            # 设置快捷方式属性
            shortcut.Targetpath = target_path
            shortcut.WorkingDirectory = working_dir
            shortcut.Arguments = arguments
            shortcut.Description = f"浏览器多账号绿色版 - {description}"

            # 设置图标
            if icon_path and os.path.exists(icon_path):
                shortcut.IconLocation = f"{icon_path},0"

            # 保存快捷方式
            shortcut.save()

            # 清理COM
            del shortcut
            del shell
            pythoncom.CoUninitialize()

            self.logger.info(f"COM快捷方式创建成功: {shortcut_path}")
            return True

        except Exception as e:
            self.logger.error(f"COM快捷方式创建失败: {e}")
            try:
                pythoncom.CoUninitialize()
            except:
                pass
            return False

    def _create_shortcut_fallback(self, shortcut_path: str, target_path: str,
                                 working_dir: str, arguments: str, description: str) -> bool:
        """备用方案：使用PowerShell创建快捷方式"""
        try:
            # PowerShell脚本
            ps_script = f'''
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("{shortcut_path}")
$Shortcut.TargetPath = "{target_path}"
$Shortcut.WorkingDirectory = "{working_dir}"
$Shortcut.Arguments = "{arguments}"
$Shortcut.Description = "浏览器多账号绿色版 - {description}"
$Shortcut.Save()
'''

            # 执行PowerShell脚本
            result = subprocess.run([
                "powershell", "-Command", ps_script
            ], capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                self.logger.info(f"PowerShell快捷方式创建成功: {shortcut_path}")
                return True
            else:
                self.logger.error(f"PowerShell快捷方式创建失败: {result.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"PowerShell快捷方式创建失败: {e}")
            return False

    def create_local_shortcut(self, browser_name: str, custom_icon: Optional[str] = None) -> bool:
        """
        创建本地快捷方式（在浏览器目录内）

        Args:
            browser_name: 浏览器名称
            custom_icon: 自定义图标路径（可选）

        Returns:
            是否创建成功
        """
        try:
            browser_dir = self.browsers_dir / browser_name
            if not browser_dir.exists():
                self.logger.error(f"浏览器 {browser_name} 不存在")
                return False

            # 快捷方式文件路径
            shortcut_path = browser_dir / f"{browser_name}.lnk"

            # 目标程序路径（使用相对路径）
            target_path = "Chrome-bin\\GoogleChromePortable.exe"

            # 工作目录（当前目录）
            working_dir = "."

            # 启动参数（优化，移除安全风险参数）
            arguments = f'--user-data-dir="Data_{browser_name}" --no-default-browser-check --disable-default-apps --disable-background-timer-throttling'

            # 图标路径
            icon_path = custom_icon or self._find_browser_icon(browser_dir)

            # 创建快捷方式
            if COM_AVAILABLE:
                success = self._create_shortcut_com(
                    str(shortcut_path), target_path, working_dir,
                    arguments, icon_path, browser_name
                )
            else:
                success = self._create_shortcut_fallback(
                    str(shortcut_path), target_path, working_dir,
                    arguments, browser_name
                )

            if success:
                self.logger.info(f"本地快捷方式创建成功: {shortcut_path}")
                return True
            else:
                self.logger.error("本地快捷方式创建失败")
                return False

        except Exception as e:
            self.logger.error(f"创建本地快捷方式失败: {e}")
            return False

    def delete_desktop_shortcut(self, browser_name: str) -> bool:
        """
        删除桌面快捷方式

        Args:
            browser_name: 浏览器名称

        Returns:
            是否删除成功
        """
        try:
            desktop_path = self._get_desktop_path()
            if not desktop_path:
                return False

            shortcut_path = desktop_path / f"{browser_name}.lnk"

            if shortcut_path.exists():
                shortcut_path.unlink()
                self.logger.info(f"桌面快捷方式删除成功: {shortcut_path}")
                return True
            else:
                self.logger.warning(f"桌面快捷方式不存在: {shortcut_path}")
                return True  # 不存在也算成功

        except Exception as e:
            self.logger.error(f"删除桌面快捷方式失败: {e}")
            return False

    def update_shortcut_icon(self, browser_name: str, new_icon_path: str) -> bool:
        """
        更新快捷方式图标

        Args:
            browser_name: 浏览器名称
            new_icon_path: 新图标路径

        Returns:
            是否更新成功
        """
        try:
            # 更新桌面快捷方式
            desktop_success = self.create_desktop_shortcut(browser_name, new_icon_path)

            # 更新本地快捷方式
            local_success = self.create_local_shortcut(browser_name, new_icon_path)

            if desktop_success and local_success:
                self.logger.info(f"快捷方式图标更新成功: {browser_name}")
                return True
            else:
                self.logger.warning(f"快捷方式图标部分更新失败: {browser_name}")
                return False

        except Exception as e:
            self.logger.error(f"更新快捷方式图标失败: {e}")
            return False

# 全局快捷方式管理器实例
shortcut_manager = ShortcutManager()

def create_desktop_shortcut(browser_name: str, custom_icon: Optional[str] = None) -> bool:
    """快捷函数：创建桌面快捷方式"""
    return shortcut_manager.create_desktop_shortcut(browser_name, custom_icon)

def create_local_shortcut(browser_name: str, custom_icon: Optional[str] = None) -> bool:
    """快捷函数：创建本地快捷方式"""
    return shortcut_manager.create_local_shortcut(browser_name, custom_icon)

if __name__ == "__main__":
    # 测试快捷方式管理器
    print("🔗 快捷方式管理器测试")
    print(f"COM组件支持: {'✅' if COM_AVAILABLE else '❌'}")
    print(f"项目根目录: {shortcut_manager.project_root}")
    print(f"浏览器目录: {shortcut_manager.browsers_dir}")

    # 测试桌面路径获取
    desktop = shortcut_manager._get_desktop_path()
    print(f"桌面路径: {desktop}")

    print("✅ 快捷方式管理器测试完成")
