#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome下载功能测试
测试Chrome Portable下载功能是否正常工作

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import sys
import time

# 导入核心模块
try:
    from Chrome更新管理器 import chrome_updater
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_download():
    """测试下载功能"""
    print("🔄 Chrome Portable下载功能测试")
    print("=" * 50)
    
    # 1. 检查版本信息
    print("\n1. 检查版本信息...")
    current = chrome_updater.get_current_version()
    latest = chrome_updater.check_latest_version()
    
    print(f"当前版本: {current or '未检测到'}")
    print(f"最新版本: {latest or '检测失败'}")
    
    if not current or not latest:
        print("❌ 版本检测失败，无法继续测试")
        return False
    
    # 2. 比较版本
    print("\n2. 比较版本...")
    need_update = chrome_updater.compare_versions(current, latest) == -1
    print(f"需要更新: {'是' if need_update else '否'}")
    
    if not need_update:
        print("✅ Chrome已是最新版本，无需更新")
        return True
    
    # 3. 获取下载链接
    print("\n3. 获取下载链接...")
    download_url = chrome_updater.get_download_url()
    if download_url:
        print(f"下载链接: {download_url}")
    else:
        print("❌ 无法获取下载链接")
        return False
    
    # 4. 询问是否下载
    print("\n4. 下载确认...")
    response = input("是否开始下载测试？(y/N): ").strip().lower()
    if response != 'y':
        print("⏹️ 用户取消下载测试")
        return True
    
    # 5. 开始下载
    print("\n5. 开始下载...")
    print("📥 正在下载Chrome Portable...")
    
    def progress_callback(progress, message):
        """进度回调"""
        print(f"\r[{progress:3d}%] {message}", end="", flush=True)
        if progress == 100:
            print()  # 换行
    
    try:
        success = chrome_updater.download_chrome(progress_callback)
        
        if success:
            print("✅ 下载完成！")
            
            # 检查下载文件
            download_file = chrome_updater.temp_dir / "GoogleChromePortable.paf.exe"
            if download_file.exists():
                file_size = download_file.stat().st_size / (1024 * 1024)  # MB
                print(f"📁 下载文件: {download_file}")
                print(f"📊 文件大小: {file_size:.1f} MB")
                
                # 询问是否删除测试文件
                response = input("\n是否删除测试下载文件？(Y/n): ").strip().lower()
                if response != 'n':
                    download_file.unlink()
                    print("🗑️ 测试文件已删除")
                else:
                    print("💾 测试文件已保留")
                
                return True
            else:
                print("❌ 下载文件不存在")
                return False
        else:
            print("❌ 下载失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 下载过程中发生错误: {e}")
        return False

def test_version_api():
    """测试版本API"""
    print("\n🔍 版本API测试")
    print("-" * 30)
    
    try:
        import requests
        
        # 测试Google Chrome版本API
        api_url = "https://versionhistory.googleapis.com/v1/chrome/platforms/win/channels/stable/versions"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        print("📡 正在连接Google版本API...")
        response = requests.get(api_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if 'versions' in data and len(data['versions']) > 0:
                latest_version = data['versions'][0]['version']
                print(f"✅ API响应正常")
                print(f"📋 最新版本: {latest_version}")
                print(f"📊 版本数量: {len(data['versions'])}")
                return True
            else:
                print("❌ API响应格式异常")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_download_sources():
    """测试下载源"""
    print("\n🌐 下载源测试")
    print("-" * 30)
    
    download_urls = [
        "https://portableapps.com/downloading/?app=GoogleChromePortable",
        "https://portableapps.com/redirect/?a=GoogleChromePortable&s=s&d=pa&f=GoogleChromePortable_online.paf.exe",
        "https://downloads.portableapps.com/GoogleChromePortable_online.paf.exe"
    ]
    
    try:
        import requests
        
        for i, url in enumerate(download_urls, 1):
            print(f"🔗 测试下载源 {i}: ", end="")
            try:
                response = requests.head(url, timeout=10)
                if response.status_code == 200:
                    print("✅ 可用")
                elif response.status_code in [301, 302]:
                    print("🔄 重定向")
                else:
                    print(f"❌ 状态码: {response.status_code}")
            except Exception as e:
                print(f"❌ 连接失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 下载源测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Chrome Portable下载功能完整测试")
    print("=" * 60)
    
    # 测试项目
    tests = [
        ("版本API测试", test_version_api),
        ("下载源测试", test_download_sources),
        ("完整下载测试", test_download)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 开始 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ 通过" if result else "❌ 失败"
            print(f"📊 {test_name}: {status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"📊 {test_name}: ❌ 异常 - {e}")
    
    # 测试总结
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n📊 总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Chrome下载功能正常")
    else:
        print("⚠️ 部分测试失败，请检查网络连接和配置")
    
    print("\n💡 提示：如果下载测试失败，可能是网络问题或下载源暂时不可用")
    print("   可以稍后重试或检查防火墙设置")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")
    finally:
        input("\n按回车键退出...")
