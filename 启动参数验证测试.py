#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动参数验证测试
验证修复后的启动参数是否正确

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import os
import sys
from pathlib import Path
import re

# 导入核心模块
try:
    from 配置管理器 import config_manager
except ImportError as e:
    print(f"❌ 导入核心模块失败: {e}")
    sys.exit(1)

class LaunchParameterValidator:
    """启动参数验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.project_root = config_manager.get_project_root()
        self.browsers_dir = self.project_root / "浏览器实例"
        
        print("🔍 启动参数验证测试")
        print("=" * 50)
    
    def scan_all_scripts(self):
        """扫描所有启动脚本"""
        if not self.browsers_dir.exists():
            print("❌ 浏览器实例目录不存在")
            return []
        
        scripts = []
        for browser_dir in self.browsers_dir.iterdir():
            if browser_dir.is_dir():
                script_file = browser_dir / f"{browser_dir.name}.bat"
                if script_file.exists():
                    scripts.append((browser_dir.name, script_file))
        
        return scripts
    
    def analyze_script(self, script_file: Path):
        """分析启动脚本"""
        try:
            with open(script_file, 'r', encoding='gbk') as f:
                content = f.read()
            
            # 检查各种参数
            analysis = {
                'has_disable_web_security': '--disable-web-security' in content,
                'has_user_data_dir': '--user-data-dir=' in content,
                'has_no_default_browser_check': '--no-default-browser-check' in content,
                'has_disable_default_apps': '--disable-default-apps' in content,
                'has_disable_background_timer_throttling': '--disable-background-timer-throttling' in content,
                'content': content.strip()
            }
            
            return analysis
            
        except Exception as e:
            print(f"  ❌ 分析失败: {e}")
            return None
    
    def validate_script(self, browser_name: str, analysis: dict):
        """验证脚本是否符合要求"""
        print(f"\n🔍 验证浏览器: {browser_name}")
        
        issues = []
        good_points = []
        
        # 检查是否还有安全风险参数
        if analysis['has_disable_web_security']:
            issues.append("❌ 仍包含 --disable-web-security 参数")
        else:
            good_points.append("✅ 已移除 --disable-web-security 参数")
        
        # 检查必需参数
        if analysis['has_user_data_dir']:
            good_points.append("✅ 包含 --user-data-dir 参数")
        else:
            issues.append("❌ 缺少 --user-data-dir 参数")
        
        # 检查优化参数
        if analysis['has_no_default_browser_check']:
            good_points.append("✅ 包含 --no-default-browser-check 参数")
        else:
            issues.append("⚠️  缺少 --no-default-browser-check 参数")
        
        if analysis['has_disable_default_apps']:
            good_points.append("✅ 包含 --disable-default-apps 参数")
        else:
            issues.append("⚠️  缺少 --disable-default-apps 参数")
        
        if analysis['has_disable_background_timer_throttling']:
            good_points.append("✅ 包含 --disable-background-timer-throttling 参数")
        else:
            issues.append("⚠️  缺少 --disable-background-timer-throttling 参数")
        
        # 显示结果
        for point in good_points:
            print(f"  {point}")
        
        for issue in issues:
            print(f"  {issue}")
        
        # 显示完整内容
        print(f"  📄 脚本内容:")
        for line in analysis['content'].split('\n'):
            if line.strip():
                print(f"     {line}")
        
        return len(issues) == 0
    
    def run_validation(self):
        """运行完整验证"""
        print("🔍 扫描所有浏览器启动脚本...")
        
        scripts = self.scan_all_scripts()
        if not scripts:
            print("📭 未找到浏览器实例")
            return
        
        print(f"\n📋 找到 {len(scripts)} 个浏览器实例")
        
        all_valid = True
        valid_count = 0
        
        for browser_name, script_file in scripts:
            analysis = self.analyze_script(script_file)
            if analysis:
                is_valid = self.validate_script(browser_name, analysis)
                if is_valid:
                    valid_count += 1
                else:
                    all_valid = False
            else:
                all_valid = False
        
        # 总结
        print(f"\n📊 验证结果:")
        print(f"  ✅ 通过验证: {valid_count} 个")
        print(f"  ❌ 未通过验证: {len(scripts) - valid_count} 个")
        
        if all_valid:
            print(f"\n🎉 所有浏览器实例的启动参数都已正确修复！")
            print(f"   • 移除了安全风险参数 --disable-web-security")
            print(f"   • 添加了优化参数")
            print(f"   • 启动浏览器不会再显示安全警告")
        else:
            print(f"\n⚠️  部分浏览器实例需要进一步修复")
        
        return all_valid
    
    def show_expected_format(self):
        """显示期望的脚本格式"""
        print(f"\n📋 期望的启动脚本格式:")
        print(f"=" * 40)
        print(f"@echo off")
        print(f"cd /d \"%~dp0\"")
        print(f"start \"\" \"Chrome-bin\\GoogleChromePortable.exe\" \\")
        print(f"  --user-data-dir=\"Data_浏览器名称\" \\")
        print(f"  --no-default-browser-check \\")
        print(f"  --disable-default-apps \\")
        print(f"  --disable-background-timer-throttling")
        print(f"=" * 40)

def main():
    """主函数"""
    try:
        validator = LaunchParameterValidator()
        
        # 显示期望格式
        validator.show_expected_format()
        
        # 运行验证
        result = validator.run_validation()
        
        if result:
            print(f"\n✅ 验证通过！启动参数修复成功！")
        else:
            print(f"\n❌ 验证失败，需要进一步修复")
            print(f"💡 建议运行: python 启动参数修复工具.py")
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 验证工具运行失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
