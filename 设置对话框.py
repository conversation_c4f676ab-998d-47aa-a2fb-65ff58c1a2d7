#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - 设置对话框
系统设置的图形界面，支持分类设置、实时预览、导入导出等功能

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Callable

# 导入核心模块
try:
    from 设置管理器 import settings_manager, get_setting, set_setting
    from 主题管理器 import theme_manager, apply_theme, switch_theme
    from 配置管理器 import config_manager
except ImportError as e:
    print(f"❌ 导入核心模块失败: {e}")
    sys.exit(1)

class SettingsDialog:
    """设置对话框 - 系统设置的图形界面"""

    def __init__(self, parent=None, on_settings_changed: Optional[Callable] = None):
        """
        初始化设置对话框

        Args:
            parent: 父窗口
            on_settings_changed: 设置更改时的回调函数
        """
        self.parent = parent
        self.on_settings_changed = on_settings_changed
        self.dialog = None
        self.notebook = None
        self.setting_widgets = {}  # 存储设置控件的引用
        self.original_values = {}  # 存储原始值，用于取消时恢复

        self.create_dialog()

    def create_dialog(self):
        """创建设置对话框"""
        # 创建对话框窗口
        if self.parent:
            self.dialog = tk.Toplevel(self.parent)
            self.dialog.transient(self.parent)
            self.dialog.grab_set()
        else:
            self.dialog = tk.Tk()

        self.dialog.title("⚙️ 系统设置")
        self.dialog.geometry("700x600")
        self.dialog.resizable(True, True)

        # 居中显示
        self.center_dialog()

        # 应用主题
        theme_manager.apply_theme_to_window(self.dialog)

        # 创建界面
        self.create_ui()

        # 加载当前设置值
        self.load_current_settings()

    def center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

    def create_ui(self):
        """创建用户界面"""
        # 主容器
        main_frame = tk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 标题
        title_label = tk.Label(main_frame, text="⚙️ 系统设置",
                              font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(pady=(0, 15))

        # 创建分类标签页
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 创建各个设置分类页面
        self.create_settings_pages()

        # 底部按钮区域
        self.create_bottom_buttons(main_frame)

        # 应用主题
        themed_widgets = [
            (main_frame, "frame"),
            (title_label, "label")
        ]

        for widget, widget_type in themed_widgets:
            apply_theme(widget, widget_type)

    def create_settings_pages(self):
        """创建设置分类页面"""
        schema = settings_manager.get_settings_schema()

        for category, category_info in schema.items():
            # 创建分类页面
            page_frame = ttk.Frame(self.notebook)
            self.notebook.add(page_frame, text=category_info["title"])

            # 创建滚动区域
            canvas = tk.Canvas(page_frame)
            scrollbar = ttk.Scrollbar(page_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # 分类描述
            desc_label = tk.Label(scrollable_frame, text=category_info["description"],
                                 font=("Microsoft YaHei", 9), wraplength=600)
            desc_label.pack(anchor=tk.W, padx=10, pady=(10, 20))

            # 创建设置项
            self.setting_widgets[category] = {}
            self.create_category_settings(scrollable_frame, category, category_info["items"])

            # 布局滚动区域
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # 绑定鼠标滚轮事件
            def on_mousewheel(event):
                canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            canvas.bind("<MouseWheel>", on_mousewheel)

            # 应用主题
            apply_theme(canvas, "default")
            apply_theme(desc_label, "label")

    def create_category_settings(self, parent, category: str, items: Dict[str, Any]):
        """创建某个分类的设置项"""
        for key, item_info in items.items():
            # 设置项容器
            item_frame = tk.Frame(parent)
            item_frame.pack(fill=tk.X, padx=10, pady=5)

            # 设置项标题
            title_label = tk.Label(item_frame, text=item_info["title"],
                                  font=("Microsoft YaHei", 10, "bold"))
            title_label.pack(anchor=tk.W)

            # 设置项描述
            if "description" in item_info:
                desc_label = tk.Label(item_frame, text=item_info["description"],
                                     font=("Microsoft YaHei", 8), fg="gray")
                desc_label.pack(anchor=tk.W, pady=(0, 5))

            # 根据类型创建控件
            widget = self.create_setting_widget(item_frame, category, key, item_info)
            if widget:
                self.setting_widgets[category][key] = widget

            # 应用主题
            apply_theme(item_frame, "frame")
            apply_theme(title_label, "label")
            if "description" in item_info:
                apply_theme(desc_label, "label")

    def create_setting_widget(self, parent, category: str, key: str, item_info: Dict[str, Any]):
        """根据设置类型创建对应的控件"""
        setting_type = item_info["type"]

        if setting_type == "boolean":
            # 复选框
            var = tk.BooleanVar()
            widget = tk.Checkbutton(parent, variable=var,
                                   font=("Microsoft YaHei", 9))
            widget.pack(anchor=tk.W)
            widget.var = var
            apply_theme(widget, "default")
            return widget

        elif setting_type == "integer":
            # 数字输入框
            frame = tk.Frame(parent)
            frame.pack(fill=tk.X)

            var = tk.StringVar()
            widget = tk.Entry(frame, textvariable=var, width=10,
                             font=("Microsoft YaHei", 9))
            widget.pack(side=tk.LEFT)

            # 范围提示
            if "min" in item_info and "max" in item_info:
                range_label = tk.Label(frame,
                                      text=f"({item_info['min']}-{item_info['max']})",
                                      font=("Microsoft YaHei", 8), fg="gray")
                range_label.pack(side=tk.LEFT, padx=(5, 0))
                apply_theme(range_label, "label")

            widget.var = var
            apply_theme(frame, "frame")
            apply_theme(widget, "entry")
            return widget

        elif setting_type == "choice":
            # 下拉选择框
            var = tk.StringVar()
            choices = [choice[1] for choice in item_info["choices"]]  # 显示文本
            widget = ttk.Combobox(parent, textvariable=var, values=choices,
                                 state="readonly", font=("Microsoft YaHei", 9))
            widget.pack(anchor=tk.W, fill=tk.X)
            widget.var = var
            widget.choices_map = {choice[1]: choice[0] for choice in item_info["choices"]}
            widget.reverse_map = {choice[0]: choice[1] for choice in item_info["choices"]}
            return widget

        elif setting_type == "file":
            # 文件选择
            frame = tk.Frame(parent)
            frame.pack(fill=tk.X)

            var = tk.StringVar()
            entry = tk.Entry(frame, textvariable=var, font=("Microsoft YaHei", 9))
            entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

            def browse_file():
                file_filter = item_info.get("filter", "所有文件 (*.*)|*.*")
                filename = filedialog.askopenfilename(
                    title=f"选择{item_info['title']}",
                    filetypes=[(file_filter.split("|")[0], file_filter.split("|")[1])]
                )
                if filename:
                    var.set(filename)

            browse_btn = tk.Button(frame, text="浏览...", command=browse_file,
                                  font=("Microsoft YaHei", 9))
            browse_btn.pack(side=tk.RIGHT, padx=(5, 0))

            entry.var = var
            apply_theme(frame, "frame")
            apply_theme(entry, "entry")
            apply_theme(browse_btn, "button")
            return entry

        elif setting_type == "directory":
            # 目录选择
            frame = tk.Frame(parent)
            frame.pack(fill=tk.X)

            var = tk.StringVar()
            entry = tk.Entry(frame, textvariable=var, font=("Microsoft YaHei", 9))
            entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

            def browse_directory():
                directory = filedialog.askdirectory(title=f"选择{item_info['title']}")
                if directory:
                    var.set(directory)

            browse_btn = tk.Button(frame, text="浏览...", command=browse_directory,
                                  font=("Microsoft YaHei", 9))
            browse_btn.pack(side=tk.RIGHT, padx=(5, 0))

            entry.var = var
            apply_theme(frame, "frame")
            apply_theme(entry, "entry")
            apply_theme(browse_btn, "button")
            return entry

        else:
            # 默认文本输入框
            var = tk.StringVar()
            widget = tk.Entry(parent, textvariable=var, font=("Microsoft YaHei", 9))
            widget.pack(anchor=tk.W, fill=tk.X)
            widget.var = var
            apply_theme(widget, "entry")
            return widget

    def create_bottom_buttons(self, parent):
        """创建底部按钮"""
        button_frame = tk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 左侧按钮
        left_frame = tk.Frame(button_frame)
        left_frame.pack(side=tk.LEFT)

        import_btn = tk.Button(left_frame, text="📥 导入", command=self.import_settings,
                              width=10, font=("Microsoft YaHei", 9))
        import_btn.pack(side=tk.LEFT, padx=(0, 5))

        export_btn = tk.Button(left_frame, text="📤 导出", command=self.export_settings,
                              width=10, font=("Microsoft YaHei", 9))
        export_btn.pack(side=tk.LEFT, padx=(0, 5))

        reset_btn = tk.Button(left_frame, text="🔄 重置", command=self.reset_settings,
                             width=10, font=("Microsoft YaHei", 9))
        reset_btn.pack(side=tk.LEFT)

        # 右侧按钮
        right_frame = tk.Frame(button_frame)
        right_frame.pack(side=tk.RIGHT)

        cancel_btn = tk.Button(right_frame, text="取消", command=self.cancel,
                              width=10, font=("Microsoft YaHei", 9))
        cancel_btn.pack(side=tk.RIGHT, padx=(5, 0))

        apply_btn = tk.Button(right_frame, text="应用", command=self.apply_settings,
                             width=10, font=("Microsoft YaHei", 9))
        apply_btn.pack(side=tk.RIGHT, padx=(5, 0))

        ok_btn = tk.Button(right_frame, text="确定", command=self.ok,
                          width=10, font=("Microsoft YaHei", 9))
        ok_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # 应用主题
        themed_widgets = [
            (button_frame, "frame"),
            (left_frame, "frame"),
            (right_frame, "frame"),
            (import_btn, "button"),
            (export_btn, "button"),
            (reset_btn, "button"),
            (cancel_btn, "button"),
            (apply_btn, "button"),
            (ok_btn, "button")
        ]

        for widget, widget_type in themed_widgets:
            apply_theme(widget, widget_type)

    def load_current_settings(self):
        """加载当前设置值到界面"""
        schema = settings_manager.get_settings_schema()

        for category, category_info in schema.items():
            if category not in self.setting_widgets:
                continue

            for key, item_info in category_info["items"].items():
                if key not in self.setting_widgets[category]:
                    continue

                widget = self.setting_widgets[category][key]
                current_value = get_setting(category, key)

                # 保存原始值
                if category not in self.original_values:
                    self.original_values[category] = {}
                self.original_values[category][key] = current_value

                # 设置控件值
                self.set_widget_value(widget, item_info, current_value)

    def set_widget_value(self, widget, item_info: Dict[str, Any], value: Any):
        """设置控件的值"""
        setting_type = item_info["type"]

        try:
            if setting_type == "boolean":
                widget.var.set(value)
            elif setting_type == "integer":
                widget.var.set(str(value))
            elif setting_type == "choice":
                # 将值转换为显示文本
                display_text = widget.reverse_map.get(value, value)
                widget.var.set(display_text)
            else:
                widget.var.set(str(value))
        except Exception as e:
            print(f"设置控件值失败: {e}")

    def get_widget_value(self, widget, item_info: Dict[str, Any]) -> Any:
        """获取控件的值"""
        setting_type = item_info["type"]

        try:
            if setting_type == "boolean":
                return widget.var.get()
            elif setting_type == "integer":
                return int(widget.var.get())
            elif setting_type == "choice":
                # 将显示文本转换为值
                display_text = widget.var.get()
                return widget.choices_map.get(display_text, display_text)
            else:
                return widget.var.get()
        except Exception as e:
            print(f"获取控件值失败: {e}")
            return item_info.get("default")

    def apply_settings(self):
        """应用设置"""
        try:
            schema = settings_manager.get_settings_schema()
            changed_settings = []

            for category, category_info in schema.items():
                if category not in self.setting_widgets:
                    continue

                for key, item_info in category_info["items"].items():
                    if key not in self.setting_widgets[category]:
                        continue

                    widget = self.setting_widgets[category][key]
                    new_value = self.get_widget_value(widget, item_info)

                    # 验证并保存设置
                    if settings_manager.validate_setting(category, key, new_value):
                        if set_setting(category, key, new_value):
                            changed_settings.append(f"{category}.{key}")
                    else:
                        messagebox.showerror("错误",
                                           f"设置值无效: {item_info['title']} = {new_value}")
                        return False

            # 通知设置已更改
            if self.on_settings_changed and changed_settings:
                self.on_settings_changed(changed_settings)

            messagebox.showinfo("成功", f"设置已保存\n更改了 {len(changed_settings)} 项设置")
            return True

        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败: {e}")
            return False

    def ok(self):
        """确定按钮"""
        if self.apply_settings():
            self.dialog.destroy()

    def cancel(self):
        """取消按钮"""
        self.dialog.destroy()

    def reset_settings(self):
        """重置设置"""
        result = messagebox.askyesno("确认重置",
                                   "确定要重置所有设置为默认值吗？\n此操作不可撤销！")
        if result:
            if settings_manager.reset_all_to_defaults():
                self.load_current_settings()
                messagebox.showinfo("成功", "所有设置已重置为默认值")
            else:
                messagebox.showerror("错误", "重置设置失败")

    def import_settings(self):
        """导入设置"""
        file_path = filedialog.askopenfilename(
            title="导入设置",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            if settings_manager.import_settings(file_path):
                self.load_current_settings()
                messagebox.showinfo("成功", "设置导入成功")
            else:
                messagebox.showerror("错误", "导入设置失败")

    def export_settings(self):
        """导出设置"""
        file_path = filedialog.asksaveasfilename(
            title="导出设置",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            if settings_manager.export_settings(file_path):
                messagebox.showinfo("成功", "设置导出成功")
            else:
                messagebox.showerror("错误", "导出设置失败")

    def show(self):
        """显示对话框"""
        if self.dialog:
            self.dialog.mainloop()

def show_settings_dialog(parent=None, on_settings_changed=None):
    """显示设置对话框的便捷函数"""
    dialog = SettingsDialog(parent, on_settings_changed)
    dialog.show()

if __name__ == "__main__":
    # 测试设置对话框
    print("⚙️ 启动设置对话框测试...")
    show_settings_dialog()
