#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - 设置管理器
系统设置的统一管理，包括验证、保存、导入导出等功能

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import json
import logging
import os
import shutil
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from tkinter import messagebox, filedialog

# 导入配置管理器
try:
    from 配置管理器 import config_manager, get_config, set_config
except ImportError:
    print("❌ 无法导入配置管理器，请确保配置管理器.py文件存在")
    exit(1)

class SettingsManager:
    """设置管理器 - 统一管理系统所有设置"""

    def __init__(self):
        """初始化设置管理器"""
        self.config = config_manager
        self._setup_logging()
        self.settings_schema = self._define_settings_schema()
        
        self.logger.info("设置管理器初始化完成")

    def _setup_logging(self):
        """设置日志系统"""
        log_dir = self.config.get_project_root() / "日志"
        log_dir.mkdir(exist_ok=True)

        self.logger = logging.getLogger("SettingsManager")
        if not self.logger.handlers:
            handler = logging.FileHandler(log_dir / "设置管理器.log", encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

    def _define_settings_schema(self) -> Dict[str, Dict[str, Any]]:
        """定义设置项的结构和验证规则"""
        return {
            "system_settings": {
                "title": "系统设置",
                "description": "语言、主题、更新等系统级设置",
                "items": {
                    "language": {
                        "title": "界面语言",
                        "type": "choice",
                        "choices": [("zh_CN", "简体中文"), ("en_US", "English")],
                        "default": "zh_CN",
                        "description": "选择界面显示语言"
                    },
                    "theme": {
                        "title": "界面主题",
                        "type": "choice",
                        "choices": [
                            ("modern_blue", "现代蓝色主题"),
                            ("dark_theme", "深色主题"),
                            ("auto", "跟随系统")
                        ],
                        "default": "modern_blue",
                        "description": "选择界面主题风格"
                    },
                    "auto_update": {
                        "title": "自动更新",
                        "type": "boolean",
                        "default": True,
                        "description": "启用自动检查和下载更新"
                    },
                    "check_update_on_startup": {
                        "title": "启动时检查更新",
                        "type": "boolean",
                        "default": True,
                        "description": "程序启动时自动检查更新"
                    },
                    "log_level": {
                        "title": "日志级别",
                        "type": "choice",
                        "choices": [
                            ("DEBUG", "调试"),
                            ("INFO", "信息"),
                            ("WARNING", "警告"),
                            ("ERROR", "错误")
                        ],
                        "default": "INFO",
                        "description": "设置日志记录的详细程度"
                    }
                }
            },
            
            "browser_settings": {
                "title": "浏览器设置",
                "description": "Chrome路径、目录配置等浏览器相关设置",
                "items": {
                    "default_chrome_path": {
                        "title": "Chrome程序路径",
                        "type": "file",
                        "default": "./GoogleChromePortable/GoogleChromePortable.exe",
                        "description": "Chrome Portable可执行文件的路径",
                        "filter": "可执行文件 (*.exe)|*.exe"
                    },
                    "browsers_dir": {
                        "title": "浏览器实例目录",
                        "type": "directory",
                        "default": "./浏览器实例",
                        "description": "存储浏览器实例的目录"
                    },
                    "icons_dir": {
                        "title": "图标目录",
                        "type": "directory",
                        "default": "./默认图标",
                        "description": "存储图标文件的目录"
                    },
                    "backup_dir": {
                        "title": "备份目录",
                        "type": "directory",
                        "default": "./备份",
                        "description": "存储备份文件的目录"
                    }
                }
            },
            
            "icon_settings": {
                "title": "图标设置",
                "description": "图标大小、格式、下载等相关设置",
                "items": {
                    "default_icon_size": {
                        "title": "默认图标大小",
                        "type": "integer",
                        "default": 64,
                        "min": 16,
                        "max": 256,
                        "description": "图标的默认像素大小"
                    },
                    "download_timeout": {
                        "title": "下载超时时间",
                        "type": "integer",
                        "default": 30,
                        "min": 5,
                        "max": 300,
                        "description": "图标下载的超时时间（秒）"
                    },
                    "max_download_retries": {
                        "title": "最大重试次数",
                        "type": "integer",
                        "default": 3,
                        "min": 1,
                        "max": 10,
                        "description": "下载失败时的最大重试次数"
                    }
                }
            },
            
            "plugin_sync": {
                "title": "插件同步",
                "description": "插件同步相关设置",
                "items": {
                    "backup_before_sync": {
                        "title": "同步前备份",
                        "type": "boolean",
                        "default": True,
                        "description": "同步插件前自动备份目标浏览器数据"
                    },
                    "sync_timeout": {
                        "title": "同步超时时间",
                        "type": "integer",
                        "default": 60,
                        "min": 10,
                        "max": 600,
                        "description": "插件同步的超时时间（秒）"
                    }
                }
            },
            
            "gui_settings": {
                "title": "界面设置",
                "description": "窗口大小、用户体验等界面相关设置",
                "items": {
                    "window_width": {
                        "title": "窗口宽度",
                        "type": "integer",
                        "default": 900,
                        "min": 600,
                        "max": 1920,
                        "description": "主窗口的默认宽度"
                    },
                    "window_height": {
                        "title": "窗口高度",
                        "type": "integer",
                        "default": 700,
                        "min": 400,
                        "max": 1080,
                        "description": "主窗口的默认高度"
                    },
                    "window_resizable": {
                        "title": "允许调整窗口大小",
                        "type": "boolean",
                        "default": True,
                        "description": "是否允许用户调整窗口大小"
                    },
                    "show_tooltips": {
                        "title": "显示工具提示",
                        "type": "boolean",
                        "default": True,
                        "description": "鼠标悬停时显示帮助提示"
                    },
                    "animation_enabled": {
                        "title": "启用动画效果",
                        "type": "boolean",
                        "default": True,
                        "description": "启用界面动画和过渡效果"
                    }
                }
            },
            
            "feature_flags": {
                "title": "功能开关",
                "description": "各种功能的启用/禁用开关",
                "items": {
                    "enable_icon_download": {
                        "title": "启用图标下载",
                        "type": "boolean",
                        "default": True,
                        "description": "启用在线图标下载功能"
                    },
                    "enable_plugin_sync": {
                        "title": "启用插件同步",
                        "type": "boolean",
                        "default": True,
                        "description": "启用浏览器插件同步功能"
                    },
                    "enable_theme_switch": {
                        "title": "启用主题切换",
                        "type": "boolean",
                        "default": True,
                        "description": "启用界面主题切换功能"
                    },
                    "enable_language_switch": {
                        "title": "启用语言切换",
                        "type": "boolean",
                        "default": True,
                        "description": "启用界面语言切换功能"
                    },
                    "enable_auto_update": {
                        "title": "启用自动更新",
                        "type": "boolean",
                        "default": True,
                        "description": "启用自动更新检查功能"
                    },
                    "enable_desktop_shortcuts": {
                        "title": "启用桌面快捷方式",
                        "type": "boolean",
                        "default": True,
                        "description": "启用创建桌面快捷方式功能"
                    }
                }
            }
        }

    def get_setting_value(self, category: str, key: str) -> Any:
        """获取设置值"""
        try:
            config_key = f"{category}.{key}"
            schema_item = self.settings_schema[category]["items"][key]
            default_value = schema_item["default"]
            
            return get_config(config_key, default_value)
            
        except Exception as e:
            self.logger.error(f"获取设置值失败 {category}.{key}: {e}")
            return None

    def set_setting_value(self, category: str, key: str, value: Any) -> bool:
        """设置值并验证"""
        try:
            # 验证设置值
            if not self.validate_setting(category, key, value):
                return False
            
            config_key = f"{category}.{key}"
            success = set_config(config_key, value)
            
            if success:
                self.logger.info(f"设置 {category}.{key} 更新为: {value}")
            else:
                self.logger.error(f"设置 {category}.{key} 保存失败")
                
            return success
            
        except Exception as e:
            self.logger.error(f"设置值失败 {category}.{key}: {e}")
            return False

    def validate_setting(self, category: str, key: str, value: Any) -> bool:
        """验证设置值是否有效"""
        try:
            if category not in self.settings_schema:
                return False
                
            if key not in self.settings_schema[category]["items"]:
                return False
                
            schema_item = self.settings_schema[category]["items"][key]
            setting_type = schema_item["type"]
            
            if setting_type == "boolean":
                return isinstance(value, bool)
            elif setting_type == "integer":
                if not isinstance(value, int):
                    return False
                if "min" in schema_item and value < schema_item["min"]:
                    return False
                if "max" in schema_item and value > schema_item["max"]:
                    return False
                return True
            elif setting_type == "choice":
                valid_choices = [choice[0] for choice in schema_item["choices"]]
                return value in valid_choices
            elif setting_type in ["file", "directory", "string"]:
                return isinstance(value, str)
            else:
                return True
                
        except Exception as e:
            self.logger.error(f"验证设置失败 {category}.{key}: {e}")
            return False

    def get_settings_schema(self) -> Dict[str, Dict[str, Any]]:
        """获取设置结构定义"""
        return self.settings_schema

    def reset_category_to_defaults(self, category: str) -> bool:
        """重置某个分类的所有设置为默认值"""
        try:
            if category not in self.settings_schema:
                return False
                
            items = self.settings_schema[category]["items"]
            success_count = 0
            
            for key, schema_item in items.items():
                default_value = schema_item["default"]
                if self.set_setting_value(category, key, default_value):
                    success_count += 1
            
            self.logger.info(f"重置分类 {category}: {success_count}/{len(items)} 项成功")
            return success_count == len(items)
            
        except Exception as e:
            self.logger.error(f"重置分类失败 {category}: {e}")
            return False

    def reset_all_to_defaults(self) -> bool:
        """重置所有设置为默认值"""
        try:
            success_count = 0
            total_count = len(self.settings_schema)
            
            for category in self.settings_schema.keys():
                if self.reset_category_to_defaults(category):
                    success_count += 1
            
            self.logger.info(f"重置所有设置: {success_count}/{total_count} 分类成功")
            return success_count == total_count
            
        except Exception as e:
            self.logger.error(f"重置所有设置失败: {e}")
            return False

    def export_settings(self, file_path: str) -> bool:
        """导出设置到文件"""
        try:
            settings_data = {}
            
            for category in self.settings_schema.keys():
                settings_data[category] = {}
                items = self.settings_schema[category]["items"]
                
                for key in items.keys():
                    value = self.get_setting_value(category, key)
                    settings_data[category][key] = value
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(settings_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"设置导出成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出设置失败: {e}")
            return False

    def import_settings(self, file_path: str) -> bool:
        """从文件导入设置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                settings_data = json.load(f)
            
            success_count = 0
            total_count = 0
            
            for category, items in settings_data.items():
                if category in self.settings_schema:
                    for key, value in items.items():
                        total_count += 1
                        if self.set_setting_value(category, key, value):
                            success_count += 1
            
            self.logger.info(f"设置导入: {success_count}/{total_count} 项成功")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"导入设置失败: {e}")
            return False

# 全局设置管理器实例
settings_manager = SettingsManager()

def get_setting(category: str, key: str) -> Any:
    """快捷函数：获取设置值"""
    return settings_manager.get_setting_value(category, key)

def set_setting(category: str, key: str, value: Any) -> bool:
    """快捷函数：设置值"""
    return settings_manager.set_setting_value(category, key, value)

if __name__ == "__main__":
    # 测试设置管理器
    print("⚙️ 设置管理器测试")
    print(f"语言设置: {get_setting('system_settings', 'language')}")
    print(f"主题设置: {get_setting('system_settings', 'theme')}")
    print(f"窗口宽度: {get_setting('gui_settings', 'window_width')}")
    print("✅ 设置管理器测试完成")
