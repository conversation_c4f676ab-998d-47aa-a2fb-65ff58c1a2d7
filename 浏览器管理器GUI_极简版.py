#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器管理器GUI - 极简版
苹果风格的极简主义设计

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sys
from pathlib import Path

# 导入相关模块
try:
    from 浏览器管理器 import BrowserManager
    from 主题管理器 import theme_manager
    from 快捷方式管理器 import shortcut_manager
    from 图标管理器 import icon_manager
except ImportError as e:
    print(f"导入失败: {e}")
    sys.exit(1)

class MinimalBrowserGUI:
    """极简浏览器管理界面"""

    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.browser_manager = BrowserManager()
        self.selected_browser = None

        self.setup_window()
        self.setup_styles()
        self.setup_ui()
        self.refresh_browser_list()

    def setup_window(self):
        """设置窗口"""
        self.root.title("浏览器管理器")
        self.root.geometry("900x650")
        self.root.minsize(800, 550)
        self.root.configure(bg='#ffffff')

        # 居中显示
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_width() // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_height() // 2)
        self.root.geometry(f"+{x}+{y}")

    def setup_styles(self):
        """设置样式"""
        self.style = ttk.Style()

        # 使用系统字体 - 调整为更紧凑的尺寸
        title_font = ('Segoe UI', 22, 'normal')
        subtitle_font = ('Segoe UI', 12, 'normal')
        body_font = ('Segoe UI', 13, 'normal')
        button_font = ('Segoe UI', 11, 'normal')

        # 配置样式
        self.style.configure('Title.TLabel',
                           font=title_font,
                           background='#ffffff',
                           foreground='#1d1d1f')

        self.style.configure('Subtitle.TLabel',
                           font=subtitle_font,
                           background='#ffffff',
                           foreground='#8e8e93')

        self.style.configure('Browser.TLabel',
                           font=body_font,
                           background='#ffffff',
                           foreground='#1d1d1f')

        self.style.configure('Status.TLabel',
                           font=('Segoe UI', 12, 'normal'),
                           background='#ffffff',
                           foreground='#8e8e93')

        # 按钮样式
        self.style.configure('Action.TButton',
                           font=button_font,
                           padding=(12, 6),
                           relief='flat',
                           borderwidth=0)

        self.style.configure('Primary.TButton',
                           font=button_font,
                           padding=(12, 6),
                           relief='flat',
                           borderwidth=0)

    def setup_ui(self):
        """设置界面"""
        # 主容器
        main_frame = tk.Frame(self.root, bg='#ffffff')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=25)

        # 标题区域
        title_frame = tk.Frame(main_frame, bg='#ffffff')
        title_frame.pack(fill=tk.X, pady=(0, 25))

        title_label = ttk.Label(title_frame, text="浏览器", style='Title.TLabel')
        title_label.pack(side=tk.LEFT)

        subtitle_label = ttk.Label(title_frame, text="管理您的浏览器实例", style='Subtitle.TLabel')
        subtitle_label.pack(side=tk.LEFT, padx=(15, 0), pady=(6, 0))

        # 操作按钮区域
        action_frame = tk.Frame(main_frame, bg='#ffffff')
        action_frame.pack(fill=tk.X, pady=(0, 20))

        # 第一行按钮 - 主要操作
        row1_frame = tk.Frame(action_frame, bg='#ffffff')
        row1_frame.pack(fill=tk.X, pady=(0, 8))

        # 左侧主要按钮
        left_buttons = tk.Frame(row1_frame, bg='#ffffff')
        left_buttons.pack(side=tk.LEFT)

        self.create_btn = ttk.Button(left_buttons, text="新建",
                                   command=self.create_browser, style='Primary.TButton')
        self.create_btn.pack(side=tk.LEFT, padx=(0, 8))

        self.launch_btn = ttk.Button(left_buttons, text="启动",
                                   command=self.launch_browser, style='Primary.TButton')
        self.launch_btn.pack(side=tk.LEFT, padx=(0, 8))

        self.rename_btn = ttk.Button(left_buttons, text="重命名",
                                   command=self.rename_browser, style='Action.TButton')
        self.rename_btn.pack(side=tk.LEFT, padx=(0, 8))

        self.delete_btn = ttk.Button(left_buttons, text="删除",
                                   command=self.delete_browser, style='Action.TButton')
        self.delete_btn.pack(side=tk.LEFT)

        # 右侧设置按钮
        right_buttons = tk.Frame(row1_frame, bg='#ffffff')
        right_buttons.pack(side=tk.RIGHT)

        self.theme_btn = ttk.Button(right_buttons, text="主题",
                                  command=self.show_theme_dialog, style='Action.TButton')
        self.theme_btn.pack(side=tk.RIGHT, padx=(8, 0))

        self.settings_btn = ttk.Button(right_buttons, text="设置",
                                     command=self.show_settings, style='Action.TButton')
        self.settings_btn.pack(side=tk.RIGHT, padx=(8, 0))

        # 第二行按钮 - 高级功能
        row2_frame = tk.Frame(action_frame, bg='#ffffff')
        row2_frame.pack(fill=tk.X)

        # 左侧高级功能
        advanced_buttons = tk.Frame(row2_frame, bg='#ffffff')
        advanced_buttons.pack(side=tk.LEFT)

        self.desktop_btn = ttk.Button(advanced_buttons, text="发送到桌面",
                                    command=self.send_to_desktop, style='Action.TButton')
        self.desktop_btn.pack(side=tk.LEFT, padx=(0, 8))

        self.icon_btn = ttk.Button(advanced_buttons, text="设置图标",
                                 command=self.set_browser_icon, style='Action.TButton')
        self.icon_btn.pack(side=tk.LEFT, padx=(0, 8))

        self.sync_btn = ttk.Button(advanced_buttons, text="同步插件",
                                 command=self.sync_plugins, style='Action.TButton')
        self.sync_btn.pack(side=tk.LEFT, padx=(0, 8))

        self.download_btn = ttk.Button(advanced_buttons, text="图标下载",
                                     command=self.download_icons, style='Action.TButton')
        self.download_btn.pack(side=tk.LEFT)

        # 右侧刷新按钮
        refresh_frame = tk.Frame(row2_frame, bg='#ffffff')
        refresh_frame.pack(side=tk.RIGHT)

        self.refresh_btn = ttk.Button(refresh_frame, text="刷新",
                                    command=self.refresh_browser_list, style='Action.TButton')
        self.refresh_btn.pack(side=tk.RIGHT)

        # 浏览器列表区域
        list_frame = tk.Frame(main_frame, bg='#ffffff')
        list_frame.pack(fill=tk.BOTH, expand=True)

        # 列表容器
        self.list_container = tk.Frame(list_frame, bg='#f8f9fa', relief=tk.FLAT, bd=0)
        self.list_container.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # 状态栏
        status_frame = tk.Frame(main_frame, bg='#ffffff')
        status_frame.pack(fill=tk.X)

        self.status_label = ttk.Label(status_frame, text="就绪", style='Status.TLabel')
        self.status_label.pack(side=tk.LEFT)

        version_label = ttk.Label(status_frame, text="v2.2.1 | CogniGraph™", style='Status.TLabel')
        version_label.pack(side=tk.RIGHT)

    def refresh_browser_list(self):
        """刷新浏览器列表"""
        # 清空现有列表
        for widget in self.list_container.winfo_children():
            widget.destroy()

        browsers = self.browser_manager.list_browsers()

        if not browsers:
            # 空状态
            empty_frame = tk.Frame(self.list_container, bg='#f8f9fa')
            empty_frame.pack(fill=tk.BOTH, expand=True)

            empty_label = ttk.Label(empty_frame, text="暂无浏览器",
                                  style='Subtitle.TLabel')
            empty_label.pack(expand=True)
        else:
            # 浏览器项目
            for i, browser in enumerate(browsers):
                self.create_browser_item(browser, i)

        self.update_button_states()

    def create_browser_item(self, browser, index):
        """创建浏览器项目"""
        # 项目容器
        item_frame = tk.Frame(self.list_container, bg='#ffffff', relief=tk.FLAT, bd=0)
        item_frame.pack(fill=tk.X, padx=2, pady=1)

        # 内容区域
        content_frame = tk.Frame(item_frame, bg='#ffffff')
        content_frame.pack(fill=tk.X, padx=20, pady=12)

        # 浏览器名称
        name_label = ttk.Label(content_frame, text=browser['name'], style='Browser.TLabel')
        name_label.pack(side=tk.LEFT)

        # 状态
        status_color = '#34c759' if browser['status'] == '正常' else '#ff3b30'
        status_label = tk.Label(content_frame, text=browser['status'],
                              font=('Segoe UI', 11, 'normal'),
                              fg=status_color, bg='#ffffff')
        status_label.pack(side=tk.RIGHT)

        # 绑定点击事件
        def on_click(event, browser_name=browser['name']):
            self.select_browser(browser_name)

        for widget in [item_frame, content_frame, name_label]:
            widget.bind("<Button-1>", on_click)
            widget.bind("<Enter>", lambda e, frame=item_frame: frame.configure(bg='#f0f0f0'))
            widget.bind("<Leave>", lambda e, frame=item_frame: frame.configure(bg='#ffffff'))

    def select_browser(self, browser_name):
        """选择浏览器"""
        self.selected_browser = browser_name

        # 更新视觉状态
        for widget in self.list_container.winfo_children():
            if widget.winfo_class() == 'Frame':
                if hasattr(widget, 'winfo_children'):
                    content = widget.winfo_children()[0] if widget.winfo_children() else None
                    if content and hasattr(content, 'winfo_children'):
                        name_widget = content.winfo_children()[0] if content.winfo_children() else None
                        if name_widget and hasattr(name_widget, 'cget'):
                            if name_widget.cget('text') == browser_name:
                                widget.configure(bg='#007aff', relief=tk.FLAT)
                                content.configure(bg='#007aff')
                                name_widget.configure(background='#007aff', foreground='#ffffff')
                            else:
                                widget.configure(bg='#ffffff', relief=tk.FLAT)
                                content.configure(bg='#ffffff')
                                name_widget.configure(background='#ffffff', foreground='#1d1d1f')

        self.update_button_states()
        self.update_status(f"已选择: {browser_name}")

    def update_button_states(self):
        """更新按钮状态"""
        has_selection = self.selected_browser is not None

        # 需要选择浏览器的按钮
        selection_buttons = [
            self.rename_btn, self.delete_btn, self.launch_btn,
            self.desktop_btn, self.icon_btn
        ]

        for btn in selection_buttons:
            btn.configure(state='normal' if has_selection else 'disabled')

    def update_status(self, message):
        """更新状态"""
        self.status_label.configure(text=message)
        self.root.after(3000, lambda: self.status_label.configure(text="就绪"))

    def create_browser(self):
        """创建浏览器"""
        try:
            name = simpledialog.askstring("新建浏览器", "请输入浏览器名称:")
            if name and name.strip():
                browser_name = name.strip()
                if self.browser_manager.create_browser(browser_name):
                    self.refresh_browser_list()
                    self.update_status(f"已创建: {browser_name}")
                    messagebox.showinfo("成功", f"浏览器 '{browser_name}' 创建成功")
                else:
                    messagebox.showerror("错误", f"创建浏览器 '{browser_name}' 失败，可能名称已存在")
        except Exception as e:
            messagebox.showerror("错误", f"创建失败: {e}")
            print(f"创建错误详情: {e}")  # 调试信息

    def rename_browser(self):
        """重命名浏览器"""
        if not self.selected_browser:
            messagebox.showwarning("提示", "请先选择一个浏览器")
            return

        try:
            new_name = simpledialog.askstring("重命名", f"请输入新名称:",
                                            initialvalue=self.selected_browser)
            if new_name and new_name.strip() and new_name.strip() != self.selected_browser:
                if self.browser_manager.rename_browser(self.selected_browser, new_name.strip()):
                    old_name = self.selected_browser
                    self.selected_browser = new_name.strip()
                    self.refresh_browser_list()
                    self.update_status(f"已重命名: {old_name} → {new_name}")
                    messagebox.showinfo("成功", f"浏览器重命名成功: {old_name} → {new_name}")
                else:
                    messagebox.showerror("错误", "重命名失败，可能名称已存在")
        except Exception as e:
            messagebox.showerror("错误", f"重命名失败: {e}")
            print(f"重命名错误详情: {e}")  # 调试信息

    def delete_browser(self):
        """删除浏览器"""
        if not self.selected_browser:
            messagebox.showwarning("提示", "请先选择一个浏览器")
            return

        try:
            if messagebox.askyesno("确认删除", f"确定要删除 '{self.selected_browser}' 吗？\n\n此操作将删除浏览器及其所有数据，无法恢复！"):
                browser_name = self.selected_browser
                if self.browser_manager.delete_browser(self.selected_browser):
                    self.selected_browser = None
                    self.refresh_browser_list()
                    self.update_status(f"已删除: {browser_name}")
                    messagebox.showinfo("成功", f"浏览器 '{browser_name}' 删除成功")
                else:
                    messagebox.showerror("错误", "删除失败，可能浏览器正在运行")
        except Exception as e:
            messagebox.showerror("错误", f"删除失败: {e}")
            print(f"删除错误详情: {e}")  # 调试信息

    def launch_browser(self):
        """启动浏览器"""
        if not self.selected_browser:
            messagebox.showwarning("提示", "请先选择一个浏览器")
            return

        try:
            if self.browser_manager.launch_browser(self.selected_browser):
                self.update_status(f"已启动: {self.selected_browser}")
                messagebox.showinfo("成功", f"浏览器 '{self.selected_browser}' 启动成功")
            else:
                messagebox.showerror("错误", f"启动浏览器 '{self.selected_browser}' 失败")
        except Exception as e:
            messagebox.showerror("错误", f"启动失败: {e}")
            print(f"启动错误详情: {e}")  # 调试信息

    def send_to_desktop(self):
        """发送到桌面"""
        if not self.selected_browser:
            messagebox.showwarning("提示", "请先选择一个浏览器")
            return

        try:
            if shortcut_manager.create_desktop_shortcut(self.selected_browser):
                self.update_status(f"已发送到桌面: {self.selected_browser}")
                messagebox.showinfo("成功", f"浏览器 '{self.selected_browser}' 的桌面快捷方式创建成功")
            else:
                messagebox.showerror("错误", "创建桌面快捷方式失败")
        except Exception as e:
            messagebox.showerror("错误", f"发送失败: {e}")
            print(f"发送到桌面错误详情: {e}")  # 调试信息

    def set_browser_icon(self):
        """设置浏览器图标"""
        if not self.selected_browser:
            messagebox.showwarning("提示", "请先选择一个浏览器")
            return

        try:
            from tkinter import filedialog
            icon_file = filedialog.askopenfilename(
                title="选择图标文件",
                filetypes=[
                    ("图标文件", "*.ico *.png *.jpg *.jpeg"),
                    ("ICO文件", "*.ico"),
                    ("PNG文件", "*.png"),
                    ("JPG文件", "*.jpg *.jpeg"),
                    ("所有文件", "*.*")
                ]
            )

            if icon_file:
                # 验证文件存在
                from pathlib import Path
                if not Path(icon_file).exists():
                    messagebox.showerror("错误", "选择的图标文件不存在")
                    return

                # 设置图标
                if icon_manager.set_browser_icon(self.selected_browser, icon_file):
                    self.update_status(f"图标设置成功: {self.selected_browser}")
                    self.refresh_browser_list()  # 刷新列表以显示新图标
                    messagebox.showinfo("成功", f"浏览器 '{self.selected_browser}' 的图标设置成功")
                else:
                    messagebox.showerror("错误", "图标设置失败，请检查文件格式")
        except Exception as e:
            messagebox.showerror("错误", f"设置图标失败: {e}")
            print(f"图标设置错误详情: {e}")  # 调试信息

    def sync_plugins(self):
        """同步插件"""
        try:
            # 这里可以添加插件同步逻辑
            self.update_status("插件同步功能开发中...")
            messagebox.showinfo("提示", "插件同步功能正在开发中")
        except Exception as e:
            messagebox.showerror("错误", f"同步失败: {e}")

    def download_icons(self):
        """下载图标"""
        try:
            if icon_manager.download_default_icons():
                self.update_status("图标下载完成")
                messagebox.showinfo("成功", "默认图标下载完成")
            else:
                messagebox.showerror("错误", "图标下载失败")
        except Exception as e:
            messagebox.showerror("错误", f"下载失败: {e}")

    def show_theme_dialog(self):
        """显示主题对话框"""
        try:
            theme_window = tk.Toplevel(self.root)
            theme_window.title("主题设置")
            theme_window.geometry("300x200")
            theme_window.configure(bg='#ffffff')
            theme_window.transient(self.root)
            theme_window.grab_set()

            # 居中显示
            theme_window.update_idletasks()
            x = self.root.winfo_x() + (self.root.winfo_width() // 2) - (theme_window.winfo_width() // 2)
            y = self.root.winfo_y() + (self.root.winfo_height() // 2) - (theme_window.winfo_height() // 2)
            theme_window.geometry(f"+{x}+{y}")

            tk.Label(theme_window, text="主题选择", font=('Segoe UI', 14, 'bold'),
                    bg='#ffffff', fg='#1d1d1f').pack(pady=20)

            themes = ["浅色", "深色", "自动"]
            for theme in themes:
                tk.Button(theme_window, text=theme, width=15,
                         command=lambda t=theme: self.apply_theme(t, theme_window)).pack(pady=5)

        except Exception as e:
            messagebox.showerror("错误", f"主题对话框打开失败: {e}")

    def apply_theme(self, theme_name, window):
        """应用主题"""
        try:
            # 映射主题名称
            theme_map = {
                "浅色": "modern_blue",
                "深色": "dark_theme",
                "自动": self.detect_system_theme()  # 真正的系统主题检测
            }

            actual_theme = theme_map.get(theme_name, "modern_blue")

            if theme_manager.switch_theme(actual_theme):
                # 重新应用主题到整个界面
                self.apply_current_theme()
                self.update_status(f"已切换到{theme_name}主题")
                window.destroy()
            else:
                messagebox.showerror("错误", "主题切换失败")
        except Exception as e:
            messagebox.showerror("错误", f"主题切换失败: {e}")

    def detect_system_theme(self):
        """检测系统主题"""
        try:
            import winreg
            registry = winreg.ConnectRegistry(None, winreg.HKEY_CURRENT_USER)
            key = winreg.OpenKey(registry, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize")
            value, _ = winreg.QueryValueEx(key, "AppsUseLightTheme")
            winreg.CloseKey(key)

            if value == 0:  # 深色模式
                return "dark_theme"
            else:  # 浅色模式
                return "modern_blue"

        except Exception:
            # 如果无法检测，默认返回浅色主题
            return "modern_blue"

    def apply_current_theme(self):
        """应用当前主题到整个界面"""
        try:
            # 应用窗口主题
            theme_manager.apply_theme_to_window(self.root)

            # 递归应用主题到所有组件
            self._apply_theme_recursive(self.root)

            # 强制刷新界面
            self.root.update()

        except Exception as e:
            print(f"❌ 应用主题失败: {e}")

    def _apply_theme_recursive(self, widget):
        """递归应用主题到所有子组件"""
        try:
            # 根据组件类型应用主题
            widget_class = widget.winfo_class()

            if widget_class == "Button":
                theme_manager.apply_theme_to_widget(widget, "button")
            elif widget_class == "Label":
                theme_manager.apply_theme_to_widget(widget, "label")
            elif widget_class == "Frame":
                theme_manager.apply_theme_to_widget(widget, "frame")
            elif widget_class == "Entry":
                theme_manager.apply_theme_to_widget(widget, "entry")
            elif widget_class == "Text":
                theme_manager.apply_theme_to_widget(widget, "text")
            elif widget_class == "Listbox":
                theme_manager.apply_theme_to_widget(widget, "listbox")
            elif widget_class == "Toplevel":
                theme_manager.apply_theme_to_widget(widget, "window")
            else:
                theme_manager.apply_theme_to_widget(widget, "default")

            # 递归处理子组件
            for child in widget.winfo_children():
                self._apply_theme_recursive(child)

        except Exception as e:
            pass  # 忽略单个组件的错误，继续处理其他组件

    def show_settings(self):
        """显示设置对话框"""
        try:
            from 设置对话框 import SettingsDialog

            def on_settings_changed(changed_settings):
                """设置更改回调"""
                # 检查是否有主题相关设置更改
                theme_changed = any("theme" in setting for setting in changed_settings)
                language_changed = any("language" in setting for setting in changed_settings)

                if theme_changed:
                    # 重新应用主题
                    self.apply_current_theme()
                    self.update_status("主题设置已更新")

                if language_changed:
                    self.update_status("语言设置已更新，重启后生效")

                # 刷新界面
                self.refresh_browser_list()

            # 创建并显示设置对话框
            dialog = SettingsDialog(self.root, on_settings_changed)

        except ImportError as e:
            messagebox.showerror("错误", f"无法加载设置模块: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"打开设置失败: {e}")

        except Exception as e:
            messagebox.showerror("错误", f"设置对话框打开失败: {e}")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = MinimalBrowserGUI()
        app.run()
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
