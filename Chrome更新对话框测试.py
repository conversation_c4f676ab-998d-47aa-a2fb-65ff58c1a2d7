#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome更新对话框测试
直接测试Chrome更新对话框的功能

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import tkinter as tk
from tkinter import messagebox
import sys

# 导入核心模块
try:
    from Chrome更新对话框 import ChromeUpdateDialog
    from 主题管理器 import theme_manager
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

class ChromeUpdateTestApp:
    """Chrome更新对话框测试应用"""
    
    def __init__(self):
        """初始化测试应用"""
        self.root = tk.Tk()
        self.setup_window()
        self.setup_ui()
    
    def setup_window(self):
        """设置窗口"""
        self.root.title("🔄 Chrome更新对话框测试")
        self.root.geometry("400x300")
        self.root.resizable(True, True)
        
        # 应用主题
        theme_manager.apply_theme_to_window(self.root)
    
    def setup_ui(self):
        """设置界面"""
        # 主容器
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, text="🔄 Chrome更新对话框测试", 
                              font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 说明
        info_text = """
这个测试程序用于验证Chrome更新对话框的功能。

点击下面的按钮打开Chrome更新对话框，
然后测试以下功能：

1. 版本检测是否正常
2. 下载按钮是否可用
3. 下载功能是否工作
4. 安装功能是否正常

注意：实际下载会消耗网络流量，
请在网络条件良好时进行测试。
        """
        
        info_label = tk.Label(main_frame, text=info_text.strip(),
                             font=("Microsoft YaHei", 10), justify=tk.LEFT)
        info_label.pack(pady=(0, 20))
        
        # 测试按钮
        test_btn = tk.Button(main_frame, text="🚀 打开Chrome更新对话框", 
                           command=self.open_update_dialog,
                           font=("Microsoft YaHei", 12), 
                           width=25, height=2)
        test_btn.pack(pady=10)
        
        # 状态显示
        self.status_label = tk.Label(main_frame, text="准备就绪", 
                                   font=("Microsoft YaHei", 10))
        self.status_label.pack(pady=(20, 0))
        
        # 退出按钮
        exit_btn = tk.Button(main_frame, text="退出", 
                           command=self.root.quit,
                           font=("Microsoft YaHei", 10), 
                           width=15)
        exit_btn.pack(pady=(10, 0))
    
    def open_update_dialog(self):
        """打开Chrome更新对话框"""
        try:
            self.status_label.config(text="正在打开Chrome更新对话框...")
            self.root.update()
            
            def on_update_complete():
                """更新完成回调"""
                self.status_label.config(text="Chrome更新完成！")
                messagebox.showinfo("更新完成", "Chrome Portable更新完成！")
            
            # 创建并显示Chrome更新对话框
            dialog = ChromeUpdateDialog(self.root, on_update_complete)
            
            self.status_label.config(text="Chrome更新对话框已打开")
            
        except Exception as e:
            self.status_label.config(text="打开对话框失败")
            messagebox.showerror("错误", f"打开Chrome更新对话框失败：\n\n{e}")
    
    def run(self):
        """运行测试应用"""
        self.root.mainloop()

def main():
    """主函数"""
    print("🔄 Chrome更新对话框测试程序")
    print("=" * 40)
    print("这个程序将打开Chrome更新对话框进行功能测试")
    print("请在对话框中测试以下功能：")
    print("1. 🔍 检查更新 - 验证版本检测")
    print("2. 📥 下载更新 - 测试下载功能")
    print("3. 🔧 安装更新 - 测试安装功能")
    print("=" * 40)
    
    try:
        app = ChromeUpdateTestApp()
        app.run()
    except Exception as e:
        print(f"❌ 测试程序启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
