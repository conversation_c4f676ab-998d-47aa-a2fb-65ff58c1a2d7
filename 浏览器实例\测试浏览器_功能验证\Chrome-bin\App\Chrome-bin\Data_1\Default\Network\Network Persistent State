{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "1753467131", "host": "www.google.com.hk", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 2, "broken_until": "1753467132", "host": "android.clients.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 1, "broken_until": "1753467133", "host": "ogads-pa.clients6.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 1, "broken_until": "1753467135", "host": "play.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "broken_count": 2, "broken_until": "1753467139", "host": "optimizationguide-pa.googleapis.com", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL2d2dDEuY29t", false, 0], "server": "https://redirector.gvt1.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com.hk", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400532435320815", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400532441500586", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400532462379318", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://android.clients.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400532429979303", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G"}}}