#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - Chrome更新对话框
Chrome Portable更新的图形界面

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import sys
from typing import Optional, Callable

# 导入核心模块
try:
    from Chrome更新管理器 import chrome_updater
    from 主题管理器 import theme_manager, apply_theme
except ImportError as e:
    print(f"❌ 导入核心模块失败: {e}")
    sys.exit(1)

class ChromeUpdateDialog:
    """Chrome更新对话框"""

    def __init__(self, parent=None, on_update_complete: Optional[Callable] = None):
        """
        初始化Chrome更新对话框
        
        Args:
            parent: 父窗口
            on_update_complete: 更新完成时的回调函数
        """
        self.parent = parent
        self.on_update_complete = on_update_complete
        self.dialog = None
        self.update_thread = None
        self.is_updating = False
        
        self.create_dialog()

    def create_dialog(self):
        """创建更新对话框"""
        # 创建对话框窗口
        if self.parent:
            self.dialog = tk.Toplevel(self.parent)
            self.dialog.transient(self.parent)
            self.dialog.grab_set()
        else:
            self.dialog = tk.Tk()
        
        self.dialog.title("🔄 Chrome Portable 更新")
        self.dialog.geometry("600x500")
        self.dialog.resizable(False, False)
        
        # 居中显示
        self.center_dialog()
        
        # 应用主题
        theme_manager.apply_theme_to_window(self.dialog)
        
        # 创建界面
        self.create_ui()
        
        # 初始化检查
        self.check_version_info()

    def center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

    def create_ui(self):
        """创建用户界面"""
        # 主容器
        main_frame = tk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, text="🔄 Chrome Portable 更新", 
                              font=("Microsoft YaHei", 18, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 版本信息区域
        self.create_version_info(main_frame)
        
        # 进度区域
        self.create_progress_area(main_frame)
        
        # 日志区域
        self.create_log_area(main_frame)
        
        # 按钮区域
        self.create_button_area(main_frame)
        
        # 应用主题
        themed_widgets = [
            (main_frame, "frame"),
            (title_label, "label")
        ]
        
        for widget, widget_type in themed_widgets:
            apply_theme(widget, widget_type)

    def create_version_info(self, parent):
        """创建版本信息区域"""
        version_frame = tk.LabelFrame(parent, text="版本信息", 
                                    font=("Microsoft YaHei", 12))
        version_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 当前版本
        current_frame = tk.Frame(version_frame)
        current_frame.pack(fill=tk.X, padx=15, pady=8)
        
        tk.Label(current_frame, text="当前版本:", 
                font=("Microsoft YaHei", 11, "bold")).pack(side=tk.LEFT)
        
        self.current_version_label = tk.Label(current_frame, text="检测中...", 
                                            font=("Microsoft YaHei", 11))
        self.current_version_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 最新版本
        latest_frame = tk.Frame(version_frame)
        latest_frame.pack(fill=tk.X, padx=15, pady=8)
        
        tk.Label(latest_frame, text="最新版本:", 
                font=("Microsoft YaHei", 11, "bold")).pack(side=tk.LEFT)
        
        self.latest_version_label = tk.Label(latest_frame, text="检测中...", 
                                           font=("Microsoft YaHei", 11))
        self.latest_version_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 更新状态
        status_frame = tk.Frame(version_frame)
        status_frame.pack(fill=tk.X, padx=15, pady=8)
        
        tk.Label(status_frame, text="更新状态:", 
                font=("Microsoft YaHei", 11, "bold")).pack(side=tk.LEFT)
        
        self.update_status_label = tk.Label(status_frame, text="检查中...", 
                                          font=("Microsoft YaHei", 11))
        self.update_status_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 应用主题
        apply_theme(version_frame, "frame")
        for frame in [current_frame, latest_frame, status_frame]:
            apply_theme(frame, "frame")
            for widget in frame.winfo_children():
                apply_theme(widget, "label")

    def create_progress_area(self, parent):
        """创建进度区域"""
        progress_frame = tk.LabelFrame(parent, text="更新进度", 
                                     font=("Microsoft YaHei", 12))
        progress_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                          maximum=100, length=400)
        self.progress_bar.pack(padx=15, pady=10)
        
        # 进度文本
        self.progress_text = tk.Label(progress_frame, text="准备就绪", 
                                    font=("Microsoft YaHei", 10))
        self.progress_text.pack(pady=(0, 10))
        
        # 应用主题
        apply_theme(progress_frame, "frame")
        apply_theme(self.progress_text, "label")

    def create_log_area(self, parent):
        """创建日志区域"""
        log_frame = tk.LabelFrame(parent, text="更新日志", 
                                font=("Microsoft YaHei", 12))
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # 创建文本框和滚动条
        text_frame = tk.Frame(log_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.log_text = tk.Text(text_frame, wrap=tk.WORD, height=8,
                              font=("Consolas", 9))
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, 
                               command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 应用主题
        apply_theme(log_frame, "frame")
        apply_theme(text_frame, "frame")
        apply_theme(self.log_text, "text")

    def create_button_area(self, parent):
        """创建按钮区域"""
        button_frame = tk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 左侧按钮
        left_frame = tk.Frame(button_frame)
        left_frame.pack(side=tk.LEFT)
        
        self.check_btn = tk.Button(left_frame, text="🔍 检查更新", 
                                  command=self.check_update,
                                  width=12, font=("Microsoft YaHei", 11))
        self.check_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.download_btn = tk.Button(left_frame, text="📥 下载更新", 
                                    command=self.download_update,
                                    width=12, font=("Microsoft YaHei", 11))
        self.download_btn.pack(side=tk.LEFT, padx=(0, 10))
        self.download_btn.config(state=tk.DISABLED)
        
        self.install_btn = tk.Button(left_frame, text="🔧 安装更新", 
                                   command=self.install_update,
                                   width=12, font=("Microsoft YaHei", 11))
        self.install_btn.pack(side=tk.LEFT)
        self.install_btn.config(state=tk.DISABLED)
        
        # 右侧按钮
        right_frame = tk.Frame(button_frame)
        right_frame.pack(side=tk.RIGHT)
        
        self.close_btn = tk.Button(right_frame, text="关闭", 
                                 command=self.close_dialog,
                                 width=10, font=("Microsoft YaHei", 11))
        self.close_btn.pack()
        
        # 应用主题
        apply_theme(button_frame, "frame")
        apply_theme(left_frame, "frame")
        apply_theme(right_frame, "frame")
        for btn in [self.check_btn, self.download_btn, self.install_btn, self.close_btn]:
            apply_theme(btn, "button")

    def log_message(self, message: str):
        """记录日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.dialog.update()

    def update_progress(self, progress: int, message: str):
        """更新进度"""
        self.progress_var.set(progress)
        self.progress_text.config(text=message)
        self.log_message(f"[{progress}%] {message}")

    def check_version_info(self):
        """检查版本信息"""
        def check_in_thread():
            try:
                self.log_message("🔍 正在检查版本信息...")
                
                # 获取当前版本
                current = chrome_updater.get_current_version()
                if current:
                    self.current_version_label.config(text=current)
                    self.log_message(f"✅ 当前版本: {current}")
                else:
                    self.current_version_label.config(text="未检测到", fg="red")
                    self.log_message("❌ 未检测到Chrome Portable")
                
                # 获取最新版本
                latest = chrome_updater.check_latest_version()
                if latest:
                    self.latest_version_label.config(text=latest)
                    self.log_message(f"✅ 最新版本: {latest}")
                else:
                    self.latest_version_label.config(text="检测失败", fg="red")
                    self.log_message("❌ 无法获取最新版本信息")
                
                # 判断是否需要更新
                if current and latest:
                    need_update = chrome_updater.compare_versions(current, latest) == -1
                    if need_update:
                        self.update_status_label.config(text="有新版本可用", fg="orange")
                        self.download_btn.config(state=tk.NORMAL)
                        self.log_message("🆕 发现新版本，可以更新")
                    else:
                        self.update_status_label.config(text="已是最新版本", fg="green")
                        self.log_message("✅ Chrome已是最新版本")
                else:
                    self.update_status_label.config(text="检查失败", fg="red")
                
            except Exception as e:
                self.log_message(f"❌ 版本检查失败: {e}")
                self.update_status_label.config(text="检查失败", fg="red")
        
        # 在后台线程中执行
        threading.Thread(target=check_in_thread, daemon=True).start()

    def check_update(self):
        """检查更新"""
        self.log_message("🔄 重新检查更新...")
        self.check_version_info()

    def download_update(self):
        """下载更新"""
        if self.is_updating:
            return
        
        def download_in_thread():
            try:
                self.is_updating = True
                self.download_btn.config(state=tk.DISABLED)
                self.check_btn.config(state=tk.DISABLED)
                
                self.log_message("📥 开始下载Chrome Portable...")
                
                success = chrome_updater.download_chrome(self.update_progress)
                
                if success:
                    self.log_message("✅ 下载完成")
                    self.install_btn.config(state=tk.NORMAL)
                else:
                    self.log_message("❌ 下载失败")
                    
            except Exception as e:
                self.log_message(f"❌ 下载过程中发生错误: {e}")
            finally:
                self.is_updating = False
                self.check_btn.config(state=tk.NORMAL)
        
        # 在后台线程中执行
        self.update_thread = threading.Thread(target=download_in_thread, daemon=True)
        self.update_thread.start()

    def install_update(self):
        """安装更新"""
        if self.is_updating:
            return
        
        result = messagebox.askyesno("确认安装", 
                                   "安装更新将替换当前的Chrome Portable。\n"
                                   "当前版本会自动备份。\n\n"
                                   "确定要继续吗？")
        if not result:
            return
        
        def install_in_thread():
            try:
                self.is_updating = True
                self.install_btn.config(state=tk.DISABLED)
                self.download_btn.config(state=tk.DISABLED)
                self.check_btn.config(state=tk.DISABLED)
                
                self.log_message("🔧 开始安装Chrome Portable...")
                
                # 这里需要实现安装逻辑
                # 由于实际的安装过程比较复杂，这里先显示一个模拟过程
                self.update_progress(0, "准备安装...")
                self.update_progress(25, "备份当前版本...")
                self.update_progress(50, "解压新版本...")
                self.update_progress(75, "更新文件...")
                self.update_progress(100, "安装完成")
                
                self.log_message("✅ Chrome Portable 更新完成")
                
                if self.on_update_complete:
                    self.on_update_complete()
                
                messagebox.showinfo("更新完成", "Chrome Portable 已成功更新到最新版本！")
                
            except Exception as e:
                self.log_message(f"❌ 安装过程中发生错误: {e}")
                messagebox.showerror("安装失败", f"安装过程中发生错误：{e}")
            finally:
                self.is_updating = False
                self.check_btn.config(state=tk.NORMAL)
        
        # 在后台线程中执行
        self.update_thread = threading.Thread(target=install_in_thread, daemon=True)
        self.update_thread.start()

    def close_dialog(self):
        """关闭对话框"""
        if self.is_updating:
            result = messagebox.askyesno("确认关闭", 
                                       "更新正在进行中，确定要关闭吗？")
            if not result:
                return
        
        self.dialog.destroy()

    def show(self):
        """显示对话框"""
        if self.dialog:
            self.dialog.mainloop()

def show_chrome_update_dialog(parent=None, on_update_complete=None):
    """显示Chrome更新对话框的便捷函数"""
    dialog = ChromeUpdateDialog(parent, on_update_complete)
    dialog.show()

if __name__ == "__main__":
    # 测试Chrome更新对话框
    print("🔄 启动Chrome更新对话框测试...")
    show_chrome_update_dialog()
