#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题修复验证工具
验证主题切换修复是否成功

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import time

# 导入核心模块
try:
    from 配置管理器 import config_manager, get_config, set_config
    from 主题管理器 import theme_manager, apply_theme, switch_theme, get_theme_color
except ImportError as e:
    print(f"❌ 导入核心模块失败: {e}")
    sys.exit(1)

class ThemeFixVerification:
    """主题修复验证工具"""
    
    def __init__(self):
        """初始化验证工具"""
        self.root = tk.Tk()
        self.setup_window()
        self.setup_ui()
        self.run_verification()
    
    def setup_window(self):
        """设置窗口"""
        self.root.title("✅ 主题修复验证工具")
        self.root.geometry("700x500")
        self.root.resizable(True, True)
    
    def setup_ui(self):
        """设置界面"""
        # 主容器
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, text="✅ 主题修复验证工具", 
                              font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 当前状态
        self.status_label = tk.Label(main_frame, text="准备验证...", 
                                   font=("Microsoft YaHei", 12))
        self.status_label.pack(pady=(0, 15))
        
        # 测试按钮区域
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 第一行按钮
        row1 = tk.Frame(button_frame)
        row1.pack(fill=tk.X, pady=(0, 10))
        
        self.light_btn = tk.Button(row1, text="🌞 浅色主题", 
                                  command=self.test_light_theme, width=15)
        self.light_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.dark_btn = tk.Button(row1, text="🌙 深色主题", 
                                 command=self.test_dark_theme, width=15)
        self.dark_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.auto_btn = tk.Button(row1, text="🔄 跟随系统", 
                                 command=self.test_auto_theme, width=15)
        self.auto_btn.pack(side=tk.LEFT)
        
        # 第二行按钮
        row2 = tk.Frame(button_frame)
        row2.pack(fill=tk.X)
        
        self.refresh_btn = tk.Button(row2, text="🔄 强制刷新", 
                                   command=self.force_refresh, width=15)
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.verify_btn = tk.Button(row2, text="✅ 验证修复", 
                                  command=self.run_verification, width=15)
        self.verify_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.reset_btn = tk.Button(row2, text="🔧 重置", 
                                 command=self.reset_theme, width=15)
        self.reset_btn.pack(side=tk.LEFT)
        
        # 测试组件区域
        test_frame = tk.LabelFrame(main_frame, text="测试组件", 
                                 font=("Microsoft YaHei", 12))
        test_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # 各种组件测试
        test_label = tk.Label(test_frame, text="这是一个标签组件", 
                            font=("Microsoft YaHei", 10))
        test_label.pack(pady=5)
        
        test_entry = tk.Entry(test_frame, font=("Microsoft YaHei", 10))
        test_entry.insert(0, "这是一个输入框")
        test_entry.pack(pady=5)
        
        test_text = tk.Text(test_frame, height=3, font=("Microsoft YaHei", 10))
        test_text.insert("1.0", "这是一个文本框\n可以输入多行文字\n用于测试主题效果")
        test_text.pack(pady=5, fill=tk.X)
        
        test_listbox = tk.Listbox(test_frame, height=3, font=("Microsoft YaHei", 10))
        test_listbox.insert(0, "列表项 1")
        test_listbox.insert(1, "列表项 2")
        test_listbox.insert(2, "列表项 3")
        test_listbox.pack(pady=5, fill=tk.X)
        
        # 结果显示
        self.result_text = tk.Text(main_frame, height=8, wrap=tk.WORD,
                                 font=("Consolas", 9))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        # 保存组件引用
        self.test_widgets = [
            (main_frame, "frame"),
            (title_label, "label"),
            (self.status_label, "label"),
            (button_frame, "frame"),
            (row1, "frame"),
            (row2, "frame"),
            (self.light_btn, "button"),
            (self.dark_btn, "button"),
            (self.auto_btn, "button"),
            (self.refresh_btn, "button"),
            (self.verify_btn, "button"),
            (self.reset_btn, "button"),
            (test_frame, "frame"),
            (test_label, "label"),
            (test_entry, "entry"),
            (test_text, "text"),
            (test_listbox, "listbox"),
            (self.result_text, "text")
        ]
    
    def log_message(self, message: str):
        """记录消息"""
        self.result_text.insert(tk.END, f"{message}\n")
        self.result_text.see(tk.END)
        self.root.update()
    
    def update_status(self, status: str):
        """更新状态"""
        self.status_label.config(text=status)
        self.root.update()
    
    def test_light_theme(self):
        """测试浅色主题"""
        self.log_message("🌞 测试浅色主题...")
        self.update_status("切换到浅色主题")
        
        if switch_theme("modern_blue"):
            self.apply_theme()
            self.log_message("✅ 浅色主题切换成功")
            self.verify_theme_colors("modern_blue")
        else:
            self.log_message("❌ 浅色主题切换失败")
    
    def test_dark_theme(self):
        """测试深色主题"""
        self.log_message("🌙 测试深色主题...")
        self.update_status("切换到深色主题")
        
        if switch_theme("dark_theme"):
            self.apply_theme()
            self.log_message("✅ 深色主题切换成功")
            self.verify_theme_colors("dark_theme")
        else:
            self.log_message("❌ 深色主题切换失败")
    
    def test_auto_theme(self):
        """测试自动主题"""
        self.log_message("🔄 测试自动主题...")
        self.update_status("检测系统主题")
        
        try:
            import winreg
            registry = winreg.ConnectRegistry(None, winreg.HKEY_CURRENT_USER)
            key = winreg.OpenKey(registry, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize")
            value, _ = winreg.QueryValueEx(key, "AppsUseLightTheme")
            winreg.CloseKey(key)
            
            if value == 0:
                target_theme = "dark_theme"
                self.log_message("🔍 系统使用深色主题")
            else:
                target_theme = "modern_blue"
                self.log_message("🔍 系统使用浅色主题")
            
            if switch_theme(target_theme):
                self.apply_theme()
                self.log_message(f"✅ 自动切换到 {target_theme} 成功")
                self.verify_theme_colors(target_theme)
            else:
                self.log_message("❌ 自动主题切换失败")
                
        except Exception as e:
            self.log_message(f"❌ 系统主题检测失败: {e}")
    
    def force_refresh(self):
        """强制刷新主题"""
        self.log_message("🔄 强制刷新主题...")
        self.update_status("强制刷新中")
        
        try:
            # 使用新的强制刷新方法
            theme_manager.force_refresh_theme(self.root)
            self.log_message("✅ 强制刷新完成")
            self.update_status("刷新完成")
        except Exception as e:
            self.log_message(f"❌ 强制刷新失败: {e}")
    
    def apply_theme(self):
        """应用主题"""
        try:
            # 应用窗口主题
            theme_manager.apply_theme_to_window(self.root)
            
            # 应用到所有测试组件
            for widget, widget_type in self.test_widgets:
                apply_theme(widget, widget_type)
            
            # 强制刷新
            self.root.update()
            
        except Exception as e:
            self.log_message(f"❌ 应用主题失败: {e}")
    
    def verify_theme_colors(self, expected_theme: str):
        """验证主题颜色"""
        try:
            current_theme = theme_manager.get_current_theme()
            if current_theme == expected_theme:
                self.log_message(f"✅ 主题验证通过: {current_theme}")
                
                # 检查关键颜色
                bg_color = get_theme_color('bg_primary')
                text_color = get_theme_color('text_primary')
                primary_color = get_theme_color('primary')
                
                self.log_message(f"   背景色: {bg_color}")
                self.log_message(f"   文字色: {text_color}")
                self.log_message(f"   主色调: {primary_color}")
                
                # 验证深色主题的特征
                if expected_theme == "dark_theme":
                    if bg_color == "#121212" and text_color == "#FFFFFF":
                        self.log_message("✅ 深色主题颜色正确")
                    else:
                        self.log_message("⚠️ 深色主题颜色可能不正确")
                
            else:
                self.log_message(f"❌ 主题验证失败: 期望 {expected_theme}, 实际 {current_theme}")
                
        except Exception as e:
            self.log_message(f"❌ 主题验证错误: {e}")
    
    def reset_theme(self):
        """重置主题"""
        self.log_message("🔧 重置主题配置...")
        self.update_status("重置中")
        
        if set_config('system_settings.theme', 'modern_blue'):
            theme_manager.current_theme = 'modern_blue'
            self.apply_theme()
            self.log_message("✅ 主题已重置为默认浅色主题")
            self.update_status("重置完成")
        else:
            self.log_message("❌ 重置失败")
    
    def run_verification(self):
        """运行完整验证"""
        self.log_message("=" * 50)
        self.log_message("🔧 开始主题修复验证...")
        self.update_status("验证中")
        
        # 测试浅色主题
        self.test_light_theme()
        time.sleep(1)
        
        # 测试深色主题
        self.test_dark_theme()
        time.sleep(1)
        
        # 测试自动主题
        self.test_auto_theme()
        
        self.log_message("✅ 主题修复验证完成")
        self.update_status("验证完成")
    
    def run(self):
        """运行验证工具"""
        self.root.mainloop()

if __name__ == "__main__":
    print("✅ 启动主题修复验证工具...")
    try:
        tool = ThemeFixVerification()
        tool.run()
    except Exception as e:
        print(f"❌ 验证工具启动失败: {e}")
        input("按回车键退出...")
