#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动参数修复工具
修复现有浏览器实例的启动参数，移除安全风险警告

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import os
import sys
from pathlib import Path
import shutil
from typing import List, Tuple

# 导入核心模块
try:
    from 浏览器管理器 import BrowserManager
    from 配置管理器 import config_manager
except ImportError as e:
    print(f"❌ 导入核心模块失败: {e}")
    sys.exit(1)

class LaunchParameterFixer:
    """启动参数修复工具"""
    
    def __init__(self):
        """初始化修复工具"""
        self.project_root = config_manager.get_project_root()
        self.browsers_dir = self.project_root / "浏览器实例"
        self.backup_dir = self.project_root / "备份" / "启动脚本备份"
        
        # 确保备份目录存在
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        print("🔧 启动参数修复工具")
        print("=" * 50)
    
    def scan_browsers(self) -> List[Tuple[str, Path, bool]]:
        """扫描所有浏览器实例"""
        browsers = []
        
        if not self.browsers_dir.exists():
            print("❌ 浏览器实例目录不存在")
            return browsers
        
        for browser_dir in self.browsers_dir.iterdir():
            if browser_dir.is_dir():
                script_file = browser_dir / f"{browser_dir.name}.bat"
                has_security_issue = False
                
                if script_file.exists():
                    try:
                        with open(script_file, 'r', encoding='gbk') as f:
                            content = f.read()
                            if '--disable-web-security' in content:
                                has_security_issue = True
                    except Exception:
                        pass
                
                browsers.append((browser_dir.name, script_file, has_security_issue))
        
        return browsers
    
    def backup_script(self, script_file: Path) -> bool:
        """备份启动脚本"""
        try:
            backup_file = self.backup_dir / f"{script_file.stem}_{script_file.suffix}.bak"
            shutil.copy2(script_file, backup_file)
            print(f"  📁 备份: {backup_file}")
            return True
        except Exception as e:
            print(f"  ❌ 备份失败: {e}")
            return False
    
    def fix_script(self, browser_name: str, script_file: Path) -> bool:
        """修复启动脚本"""
        try:
            # 备份原文件
            if not self.backup_script(script_file):
                return False
            
            # 生成新的脚本内容
            new_content = f'''@echo off
cd /d "%~dp0"
start "" "Chrome-bin\\GoogleChromePortable.exe" --user-data-dir="Data_{browser_name}" --no-default-browser-check --disable-default-apps --disable-background-timer-throttling
'''
            
            # 写入新内容
            with open(script_file, 'w', encoding='gbk') as f:
                f.write(new_content)
            
            print(f"  ✅ 修复完成: {script_file}")
            return True
            
        except Exception as e:
            print(f"  ❌ 修复失败: {e}")
            return False
    
    def show_parameter_comparison(self):
        """显示参数对比"""
        print("\n📊 启动参数对比:")
        print("-" * 60)
        print("🔴 原来的参数 (有安全警告):")
        print("   --user-data-dir=\"Data_xxx\"")
        print("   --disable-web-security          ← 导致安全警告")
        print("   --disable-features=VizDisplayCompositor")
        print()
        print("🟢 修复后的参数 (无安全警告):")
        print("   --user-data-dir=\"Data_xxx\"")
        print("   --no-default-browser-check      ← 不检查默认浏览器")
        print("   --disable-default-apps          ← 禁用默认应用")
        print("   --disable-background-timer-throttling  ← 优化性能")
        print()
        print("✅ 修复效果:")
        print("   • 移除了 --disable-web-security 参数")
        print("   • 不再显示安全警告")
        print("   • 保持浏览器功能完整")
        print("   • 提升安全性")
        print()
    
    def run_interactive_fix(self):
        """交互式修复"""
        print("🔍 扫描浏览器实例...")
        browsers = self.scan_browsers()
        
        if not browsers:
            print("📭 未找到浏览器实例")
            return
        
        print(f"\n📋 找到 {len(browsers)} 个浏览器实例:")
        
        # 统计需要修复的数量
        need_fix = [b for b in browsers if b[2]]
        
        for i, (name, script_file, has_issue) in enumerate(browsers, 1):
            status = "🔴 需要修复" if has_issue else "🟢 已是最新"
            print(f"  {i}. {name} - {status}")
        
        if not need_fix:
            print("\n🎉 所有浏览器实例的启动参数都是最新的，无需修复！")
            return
        
        print(f"\n⚠️  发现 {len(need_fix)} 个浏览器实例需要修复")
        
        # 显示参数对比
        self.show_parameter_comparison()
        
        # 询问是否修复
        choice = input("是否修复所有有问题的浏览器实例？(y/n): ").strip().lower()
        
        if choice in ['y', 'yes', '是']:
            self.fix_all_browsers(need_fix)
        else:
            print("❌ 用户取消修复")
    
    def fix_all_browsers(self, browsers_to_fix: List[Tuple[str, Path, bool]]):
        """修复所有浏览器"""
        print(f"\n🔧 开始修复 {len(browsers_to_fix)} 个浏览器实例...")
        
        success_count = 0
        
        for name, script_file, _ in browsers_to_fix:
            print(f"\n🔧 修复浏览器: {name}")
            
            if self.fix_script(name, script_file):
                success_count += 1
            else:
                print(f"  ❌ 修复失败: {name}")
        
        print(f"\n📊 修复完成:")
        print(f"  ✅ 成功: {success_count} 个")
        print(f"  ❌ 失败: {len(browsers_to_fix) - success_count} 个")
        
        if success_count > 0:
            print(f"\n🎉 修复成功！现在启动浏览器不会再显示安全警告了！")
            print(f"📁 原始脚本已备份到: {self.backup_dir}")
        
        print("\n💡 提示:")
        print("  • 新创建的浏览器实例会自动使用优化后的参数")
        print("  • 如果需要恢复原始参数，可以从备份目录恢复")
    
    def run_auto_fix(self):
        """自动修复（非交互式）"""
        print("🔍 自动扫描并修复...")
        browsers = self.scan_browsers()
        need_fix = [b for b in browsers if b[2]]
        
        if not need_fix:
            print("🎉 所有浏览器实例都是最新的！")
            return True
        
        print(f"⚠️  发现 {len(need_fix)} 个浏览器实例需要修复")
        self.fix_all_browsers(need_fix)
        return True

def main():
    """主函数"""
    try:
        fixer = LaunchParameterFixer()
        
        # 检查命令行参数
        if len(sys.argv) > 1 and sys.argv[1] == '--auto':
            fixer.run_auto_fix()
        else:
            fixer.run_interactive_fix()
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 修复工具运行失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
