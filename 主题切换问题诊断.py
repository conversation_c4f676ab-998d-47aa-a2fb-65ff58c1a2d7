#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题切换问题诊断工具
用于诊断和修复主题切换功能的问题

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from pathlib import Path

# 导入核心模块
try:
    from 配置管理器 import config_manager, get_config, set_config
    from 主题管理器 import theme_manager, apply_theme, switch_theme, get_theme_color
except ImportError as e:
    print(f"❌ 导入核心模块失败: {e}")
    sys.exit(1)

class ThemeDiagnosticTool:
    """主题诊断工具"""
    
    def __init__(self):
        """初始化诊断工具"""
        self.root = tk.Tk()
        self.setup_window()
        self.setup_ui()
        self.run_initial_diagnosis()
    
    def setup_window(self):
        """设置窗口"""
        self.root.title("🔧 主题切换问题诊断工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
    
    def setup_ui(self):
        """设置界面"""
        # 主容器
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, text="🔧 主题切换问题诊断工具", 
                              font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 当前状态显示
        status_frame = tk.LabelFrame(main_frame, text="当前状态", 
                                   font=("Microsoft YaHei", 12))
        status_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.current_theme_label = tk.Label(status_frame, text="当前主题: 检测中...", 
                                          font=("Microsoft YaHei", 10))
        self.current_theme_label.pack(anchor=tk.W, padx=10, pady=5)
        
        self.theme_colors_label = tk.Label(status_frame, text="主题颜色: 检测中...", 
                                         font=("Microsoft YaHei", 10))
        self.theme_colors_label.pack(anchor=tk.W, padx=10, pady=5)
        
        # 测试按钮区域
        test_frame = tk.LabelFrame(main_frame, text="主题测试", 
                                 font=("Microsoft YaHei", 12))
        test_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 按钮行1
        button_row1 = tk.Frame(test_frame)
        button_row1.pack(fill=tk.X, padx=10, pady=10)
        
        self.test_light_btn = tk.Button(button_row1, text="🌞 测试浅色主题", 
                                       command=self.test_light_theme, width=15)
        self.test_light_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.test_dark_btn = tk.Button(button_row1, text="🌙 测试深色主题", 
                                      command=self.test_dark_theme, width=15)
        self.test_dark_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.test_auto_btn = tk.Button(button_row1, text="🔄 测试自动主题", 
                                      command=self.test_auto_theme, width=15)
        self.test_auto_btn.pack(side=tk.LEFT)
        
        # 按钮行2
        button_row2 = tk.Frame(test_frame)
        button_row2.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        self.apply_theme_btn = tk.Button(button_row2, text="🎨 重新应用主题", 
                                        command=self.reapply_theme, width=15)
        self.apply_theme_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.refresh_btn = tk.Button(button_row2, text="🔄 刷新状态", 
                                    command=self.refresh_status, width=15)
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.reset_btn = tk.Button(button_row2, text="🔧 重置配置", 
                                  command=self.reset_theme_config, width=15)
        self.reset_btn.pack(side=tk.LEFT)
        
        # 诊断结果显示
        result_frame = tk.LabelFrame(main_frame, text="诊断结果", 
                                   font=("Microsoft YaHei", 12))
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # 创建文本框和滚动条
        text_frame = tk.Frame(result_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.result_text = tk.Text(text_frame, wrap=tk.WORD, 
                                  font=("Consolas", 9))
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, 
                               command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 保存组件引用用于主题应用
        self.themed_widgets = [
            (main_frame, "frame"),
            (title_label, "label"),
            (status_frame, "frame"),
            (self.current_theme_label, "label"),
            (self.theme_colors_label, "label"),
            (test_frame, "frame"),
            (button_row1, "frame"),
            (button_row2, "frame"),
            (self.test_light_btn, "button"),
            (self.test_dark_btn, "button"),
            (self.test_auto_btn, "button"),
            (self.apply_theme_btn, "button"),
            (self.refresh_btn, "button"),
            (self.reset_btn, "button"),
            (result_frame, "frame"),
            (text_frame, "frame"),
            (self.result_text, "text")
        ]
    
    def log_message(self, message: str):
        """记录消息到结果显示区"""
        self.result_text.insert(tk.END, f"{message}\n")
        self.result_text.see(tk.END)
        self.root.update()
    
    def run_initial_diagnosis(self):
        """运行初始诊断"""
        self.log_message("🔧 开始主题系统诊断...")
        
        # 检查主题管理器状态
        try:
            current_theme = theme_manager.get_current_theme()
            self.log_message(f"✅ 当前主题: {current_theme}")
            
            available_themes = theme_manager.get_available_themes()
            self.log_message(f"✅ 可用主题: {list(available_themes.keys())}")
            
            # 检查主题颜色
            primary_color = get_theme_color('primary')
            bg_color = get_theme_color('bg_primary')
            text_color = get_theme_color('text_primary')
            
            self.log_message(f"✅ 主题颜色 - 主色: {primary_color}, 背景: {bg_color}, 文本: {text_color}")
            
            # 更新状态显示
            self.current_theme_label.config(text=f"当前主题: {current_theme}")
            self.theme_colors_label.config(text=f"主题颜色: 主色={primary_color}, 背景={bg_color}")
            
            # 应用当前主题
            self.reapply_theme()
            
        except Exception as e:
            self.log_message(f"❌ 诊断过程中发生错误: {e}")
    
    def test_light_theme(self):
        """测试浅色主题"""
        self.log_message("🌞 测试浅色主题...")
        try:
            if switch_theme("modern_blue"):
                self.log_message("✅ 切换到浅色主题成功")
                self.reapply_theme()
                self.refresh_status()
            else:
                self.log_message("❌ 切换到浅色主题失败")
        except Exception as e:
            self.log_message(f"❌ 浅色主题测试错误: {e}")
    
    def test_dark_theme(self):
        """测试深色主题"""
        self.log_message("🌙 测试深色主题...")
        try:
            if switch_theme("dark_theme"):
                self.log_message("✅ 切换到深色主题成功")
                self.reapply_theme()
                self.refresh_status()
            else:
                self.log_message("❌ 切换到深色主题失败")
        except Exception as e:
            self.log_message(f"❌ 深色主题测试错误: {e}")
    
    def test_auto_theme(self):
        """测试自动主题（跟随系统）"""
        self.log_message("🔄 测试自动主题...")
        try:
            # 检测系统主题
            import winreg
            try:
                registry = winreg.ConnectRegistry(None, winreg.HKEY_CURRENT_USER)
                key = winreg.OpenKey(registry, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize")
                value, _ = winreg.QueryValueEx(key, "AppsUseLightTheme")
                winreg.CloseKey(key)
                
                if value == 0:  # 深色模式
                    self.log_message("🔍 检测到系统使用深色主题")
                    target_theme = "dark_theme"
                else:  # 浅色模式
                    self.log_message("🔍 检测到系统使用浅色主题")
                    target_theme = "modern_blue"
                
                if switch_theme(target_theme):
                    self.log_message(f"✅ 自动切换到 {target_theme} 成功")
                    self.reapply_theme()
                    self.refresh_status()
                else:
                    self.log_message("❌ 自动主题切换失败")
                    
            except Exception as reg_error:
                self.log_message(f"⚠️ 无法检测系统主题，使用默认浅色主题: {reg_error}")
                self.test_light_theme()
                
        except Exception as e:
            self.log_message(f"❌ 自动主题测试错误: {e}")
    
    def reapply_theme(self):
        """重新应用主题"""
        self.log_message("🎨 重新应用主题...")
        try:
            # 应用窗口主题
            theme_manager.apply_theme_to_window(self.root)
            
            # 应用到所有组件
            for widget, widget_type in self.themed_widgets:
                apply_theme(widget, widget_type)
            
            # 强制刷新
            self.root.update()
            
            self.log_message("✅ 主题应用完成")
            
        except Exception as e:
            self.log_message(f"❌ 主题应用失败: {e}")
    
    def refresh_status(self):
        """刷新状态显示"""
        try:
            current_theme = theme_manager.get_current_theme()
            primary_color = get_theme_color('primary')
            bg_color = get_theme_color('bg_primary')
            
            self.current_theme_label.config(text=f"当前主题: {current_theme}")
            self.theme_colors_label.config(text=f"主题颜色: 主色={primary_color}, 背景={bg_color}")
            
            self.log_message(f"🔄 状态已刷新 - 主题: {current_theme}")
            
        except Exception as e:
            self.log_message(f"❌ 刷新状态失败: {e}")
    
    def reset_theme_config(self):
        """重置主题配置"""
        self.log_message("🔧 重置主题配置...")
        try:
            # 重置为默认主题
            if set_config('system_settings.theme', 'modern_blue'):
                self.log_message("✅ 主题配置已重置为默认值")
                # 重新初始化主题管理器
                theme_manager.current_theme = 'modern_blue'
                self.reapply_theme()
                self.refresh_status()
            else:
                self.log_message("❌ 重置主题配置失败")
        except Exception as e:
            self.log_message(f"❌ 重置配置错误: {e}")
    
    def run(self):
        """运行诊断工具"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🔧 启动主题切换问题诊断工具...")
    try:
        tool = ThemeDiagnosticTool()
        tool.run()
    except Exception as e:
        print(f"❌ 诊断工具启动失败: {e}")
        input("按回车键退出...")
