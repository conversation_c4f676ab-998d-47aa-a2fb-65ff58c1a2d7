#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置功能测试程序
测试设置管理器和设置对话框的功能

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import tkinter as tk
from tkinter import messagebox
import sys

# 导入核心模块
try:
    from 设置管理器 import settings_manager, get_setting, set_setting
    from 设置对话框 import SettingsDialog, show_settings_dialog
    from 主题管理器 import theme_manager, apply_theme
except ImportError as e:
    print(f"❌ 导入核心模块失败: {e}")
    sys.exit(1)

class SettingsTestApp:
    """设置功能测试应用"""
    
    def __init__(self):
        """初始化测试应用"""
        self.root = tk.Tk()
        self.setup_window()
        self.setup_ui()
        self.run_initial_tests()
    
    def setup_window(self):
        """设置窗口"""
        self.root.title("⚙️ 设置功能测试")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
    
    def setup_ui(self):
        """设置界面"""
        # 主容器
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, text="⚙️ 设置功能测试", 
                              font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 当前设置显示
        self.create_current_settings_display(main_frame)
        
        # 测试按钮区域
        self.create_test_buttons(main_frame)
        
        # 结果显示
        self.create_result_display(main_frame)
        
        # 应用主题
        theme_manager.apply_theme_to_window(self.root)
    
    def create_current_settings_display(self, parent):
        """创建当前设置显示区域"""
        settings_frame = tk.LabelFrame(parent, text="当前设置", 
                                     font=("Microsoft YaHei", 12))
        settings_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 系统设置
        system_frame = tk.Frame(settings_frame)
        system_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(system_frame, text="系统设置:", 
                font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W)
        
        self.language_label = tk.Label(system_frame, text="语言: 检测中...", 
                                     font=("Microsoft YaHei", 9))
        self.language_label.pack(anchor=tk.W, padx=20)
        
        self.theme_label = tk.Label(system_frame, text="主题: 检测中...", 
                                  font=("Microsoft YaHei", 9))
        self.theme_label.pack(anchor=tk.W, padx=20)
        
        self.auto_update_label = tk.Label(system_frame, text="自动更新: 检测中...", 
                                        font=("Microsoft YaHei", 9))
        self.auto_update_label.pack(anchor=tk.W, padx=20)
        
        # 界面设置
        gui_frame = tk.Frame(settings_frame)
        gui_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(gui_frame, text="界面设置:", 
                font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W)
        
        self.window_size_label = tk.Label(gui_frame, text="窗口大小: 检测中...", 
                                        font=("Microsoft YaHei", 9))
        self.window_size_label.pack(anchor=tk.W, padx=20)
        
        self.tooltips_label = tk.Label(gui_frame, text="工具提示: 检测中...", 
                                     font=("Microsoft YaHei", 9))
        self.tooltips_label.pack(anchor=tk.W, padx=20)
        
        # 应用主题
        apply_theme(settings_frame, "frame")
        apply_theme(system_frame, "frame")
        apply_theme(gui_frame, "frame")
        for widget in [self.language_label, self.theme_label, self.auto_update_label,
                      self.window_size_label, self.tooltips_label]:
            apply_theme(widget, "label")
    
    def create_test_buttons(self, parent):
        """创建测试按钮区域"""
        button_frame = tk.LabelFrame(parent, text="功能测试", 
                                   font=("Microsoft YaHei", 12))
        button_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 第一行按钮
        row1 = tk.Frame(button_frame)
        row1.pack(fill=tk.X, padx=10, pady=10)
        
        self.open_settings_btn = tk.Button(row1, text="🔧 打开设置对话框", 
                                         command=self.open_settings_dialog, width=18)
        self.open_settings_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.test_manager_btn = tk.Button(row1, text="⚙️ 测试设置管理器", 
                                        command=self.test_settings_manager, width=18)
        self.test_manager_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.refresh_btn = tk.Button(row1, text="🔄 刷新显示", 
                                   command=self.refresh_display, width=15)
        self.refresh_btn.pack(side=tk.LEFT)
        
        # 第二行按钮
        row2 = tk.Frame(button_frame)
        row2.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        self.test_validation_btn = tk.Button(row2, text="✅ 测试验证功能", 
                                           command=self.test_validation, width=18)
        self.test_validation_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.test_import_export_btn = tk.Button(row2, text="📁 测试导入导出", 
                                              command=self.test_import_export, width=18)
        self.test_import_export_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.reset_btn = tk.Button(row2, text="🔄 重置设置", 
                                 command=self.reset_settings, width=15)
        self.reset_btn.pack(side=tk.LEFT)
        
        # 应用主题
        apply_theme(button_frame, "frame")
        apply_theme(row1, "frame")
        apply_theme(row2, "frame")
        for btn in [self.open_settings_btn, self.test_manager_btn, self.refresh_btn,
                   self.test_validation_btn, self.test_import_export_btn, self.reset_btn]:
            apply_theme(btn, "button")
    
    def create_result_display(self, parent):
        """创建结果显示区域"""
        result_frame = tk.LabelFrame(parent, text="测试结果", 
                                   font=("Microsoft YaHei", 12))
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本框和滚动条
        text_frame = tk.Frame(result_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.result_text = tk.Text(text_frame, wrap=tk.WORD, 
                                 font=("Consolas", 9))
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, 
                               command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 应用主题
        apply_theme(result_frame, "frame")
        apply_theme(text_frame, "frame")
        apply_theme(self.result_text, "text")
    
    def log_message(self, message: str):
        """记录消息到结果显示区"""
        self.result_text.insert(tk.END, f"{message}\n")
        self.result_text.see(tk.END)
        self.root.update()
    
    def run_initial_tests(self):
        """运行初始测试"""
        self.log_message("⚙️ 开始设置功能测试...")
        
        # 测试设置管理器初始化
        try:
            schema = settings_manager.get_settings_schema()
            self.log_message(f"✅ 设置管理器初始化成功，包含 {len(schema)} 个分类")
            
            # 刷新显示
            self.refresh_display()
            
        except Exception as e:
            self.log_message(f"❌ 设置管理器初始化失败: {e}")
    
    def refresh_display(self):
        """刷新当前设置显示"""
        try:
            # 更新系统设置显示
            language = get_setting('system_settings', 'language')
            theme = get_setting('system_settings', 'theme')
            auto_update = get_setting('system_settings', 'auto_update')
            
            self.language_label.config(text=f"语言: {language}")
            self.theme_label.config(text=f"主题: {theme}")
            self.auto_update_label.config(text=f"自动更新: {'启用' if auto_update else '禁用'}")
            
            # 更新界面设置显示
            window_width = get_setting('gui_settings', 'window_width')
            window_height = get_setting('gui_settings', 'window_height')
            show_tooltips = get_setting('gui_settings', 'show_tooltips')
            
            self.window_size_label.config(text=f"窗口大小: {window_width}x{window_height}")
            self.tooltips_label.config(text=f"工具提示: {'启用' if show_tooltips else '禁用'}")
            
            self.log_message("🔄 设置显示已刷新")
            
        except Exception as e:
            self.log_message(f"❌ 刷新显示失败: {e}")
    
    def open_settings_dialog(self):
        """打开设置对话框"""
        self.log_message("🔧 打开设置对话框...")
        
        try:
            def on_settings_changed(changed_settings):
                self.log_message(f"✅ 设置已更改: {len(changed_settings)} 项")
                for setting in changed_settings:
                    self.log_message(f"   - {setting}")
                self.refresh_display()
            
            dialog = SettingsDialog(self.root, on_settings_changed)
            
        except Exception as e:
            self.log_message(f"❌ 打开设置对话框失败: {e}")
            messagebox.showerror("错误", f"打开设置对话框失败: {e}")
    
    def test_settings_manager(self):
        """测试设置管理器功能"""
        self.log_message("⚙️ 测试设置管理器功能...")
        
        try:
            # 测试获取设置
            current_theme = get_setting('system_settings', 'theme')
            self.log_message(f"✅ 获取设置成功: theme = {current_theme}")
            
            # 测试设置值
            test_value = not get_setting('system_settings', 'auto_update')
            if set_setting('system_settings', 'auto_update', test_value):
                self.log_message(f"✅ 设置值成功: auto_update = {test_value}")
                # 恢复原值
                set_setting('system_settings', 'auto_update', not test_value)
            else:
                self.log_message("❌ 设置值失败")
            
            # 测试验证功能
            valid = settings_manager.validate_setting('gui_settings', 'window_width', 800)
            invalid = settings_manager.validate_setting('gui_settings', 'window_width', -100)
            self.log_message(f"✅ 验证功能: 800 = {valid}, -100 = {invalid}")
            
        except Exception as e:
            self.log_message(f"❌ 设置管理器测试失败: {e}")
    
    def test_validation(self):
        """测试验证功能"""
        self.log_message("✅ 测试设置验证功能...")
        
        test_cases = [
            ('system_settings', 'language', 'zh_CN', True),
            ('system_settings', 'language', 'invalid_lang', False),
            ('gui_settings', 'window_width', 800, True),
            ('gui_settings', 'window_width', -100, False),
            ('system_settings', 'auto_update', True, True),
            ('system_settings', 'auto_update', 'not_boolean', False),
        ]
        
        for category, key, value, expected in test_cases:
            result = settings_manager.validate_setting(category, key, value)
            status = "✅" if result == expected else "❌"
            self.log_message(f"{status} {category}.{key} = {value}: {result}")
    
    def test_import_export(self):
        """测试导入导出功能"""
        self.log_message("📁 测试导入导出功能...")
        
        try:
            # 测试导出
            export_file = "test_settings_export.json"
            if settings_manager.export_settings(export_file):
                self.log_message(f"✅ 导出设置成功: {export_file}")
                
                # 测试导入
                if settings_manager.import_settings(export_file):
                    self.log_message(f"✅ 导入设置成功: {export_file}")
                else:
                    self.log_message(f"❌ 导入设置失败: {export_file}")
            else:
                self.log_message(f"❌ 导出设置失败: {export_file}")
                
        except Exception as e:
            self.log_message(f"❌ 导入导出测试失败: {e}")
    
    def reset_settings(self):
        """重置设置"""
        result = messagebox.askyesno("确认重置", "确定要重置所有设置为默认值吗？")
        if result:
            self.log_message("🔄 重置所有设置...")
            if settings_manager.reset_all_to_defaults():
                self.log_message("✅ 设置重置成功")
                self.refresh_display()
            else:
                self.log_message("❌ 设置重置失败")
    
    def run(self):
        """运行测试应用"""
        self.root.mainloop()

if __name__ == "__main__":
    print("⚙️ 启动设置功能测试...")
    try:
        app = SettingsTestApp()
        app.run()
    except Exception as e:
        print(f"❌ 测试应用启动失败: {e}")
        input("按回车键退出...")
