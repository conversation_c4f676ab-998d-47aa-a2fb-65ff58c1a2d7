#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - Chrome更新管理器
自动检测、下载和更新GoogleChromePortable

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import os
import sys
import json
import logging
import requests
import zipfile
import shutil
import threading
import time
from pathlib import Path
from typing import Dict, Any, Optional, Callable, Tuple
from urllib.parse import urlparse
import tempfile
import hashlib

# 导入核心模块
try:
    from 配置管理器 import config_manager, get_config, set_config
except ImportError:
    print("❌ 无法导入配置管理器，请确保配置管理器.py文件存在")
    sys.exit(1)

class ChromeUpdater:
    """Chrome Portable更新管理器"""

    def __init__(self):
        """初始化Chrome更新管理器"""
        self.config = config_manager
        self.project_root = self.config.get_project_root()
        self.chrome_dir = self.project_root / "GoogleChromePortable"
        self.backup_dir = self.project_root / "备份" / "Chrome备份"
        self.temp_dir = Path(tempfile.gettempdir()) / "ChromePortableUpdate"

        self._setup_logging()
        self._ensure_directories()

        # 下载源配置
        self.download_sources = {
            "portableapps": {
                "name": "PortableApps官网",
                "version_api": "https://portableapps.com/apps/internet/google_chrome_portable",
                "download_base": "https://portableapps.com/downloading/?app=GoogleChromePortable",
                "priority": 1
            },
            "github": {
                "name": "GitHub镜像",
                "version_api": "https://api.github.com/repos/portapps/portapps/releases/latest",
                "download_base": "https://github.com/portapps/portapps/releases/download/",
                "priority": 2
            }
        }

        # 下载状态
        self.download_progress = 0
        self.download_status = "idle"  # idle, checking, downloading, installing, completed, error
        self.download_callback = None
        self.current_version = None
        self.latest_version = None

        self.logger.info("Chrome更新管理器初始化完成")

    def _setup_logging(self):
        """设置日志系统"""
        log_dir = self.project_root / "日志"
        log_dir.mkdir(exist_ok=True)

        self.logger = logging.getLogger("ChromeUpdater")
        if not self.logger.handlers:
            handler = logging.FileHandler(log_dir / "Chrome更新管理器.log", encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

    def _ensure_directories(self):
        """确保必要的目录存在"""
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(parents=True, exist_ok=True)

    def get_current_version(self) -> Optional[str]:
        """获取当前Chrome版本"""
        try:
            chrome_exe = self.chrome_dir / "App" / "Chrome-bin" / "chrome.exe"
            if not chrome_exe.exists():
                self.logger.warning("Chrome可执行文件不存在")
                return None

            # 尝试使用win32api获取版本信息
            try:
                import win32api
                info = win32api.GetFileVersionInfo(str(chrome_exe), "\\")
                ms = info['FileVersionMS']
                ls = info['FileVersionLS']
                version = f"{win32api.HIWORD(ms)}.{win32api.LOWORD(ms)}.{win32api.HIWORD(ls)}.{win32api.LOWORD(ls)}"
                self.current_version = version
                self.logger.info(f"当前Chrome版本: {version}")
                return version
            except ImportError:
                self.logger.warning("win32api不可用，使用备用方法")

            # 备用方法：检查版本文件
            version_file = self.chrome_dir / "App" / "AppInfo" / "appinfo.ini"
            if version_file.exists():
                with open(version_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 查找版本信息
                    for line in content.split('\n'):
                        if 'DisplayVersion=' in line:
                            version = line.split('=')[1].strip()
                            self.current_version = version
                            self.logger.info(f"当前Chrome版本: {version}")
                            return version

            self.logger.error("无法获取Chrome版本信息")
            return None

        except Exception as e:
            self.logger.error(f"获取Chrome版本失败: {e}")
            return None

    def check_latest_version(self) -> Optional[str]:
        """检查最新版本"""
        try:
            self.download_status = "checking"
            self._notify_progress(0, "检查最新版本...")

            # 使用简单的方法获取Chrome版本信息
            # 这里使用一个公开的API来获取Chrome版本信息
            api_url = "https://versionhistory.googleapis.com/v1/chrome/platforms/win/channels/stable/versions"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(api_url, headers=headers, timeout=10)
            response.raise_for_status()

            data = response.json()
            if 'versions' in data and len(data['versions']) > 0:
                latest_version = data['versions'][0]['version']
                self.latest_version = latest_version
                self.logger.info(f"最新Chrome版本: {latest_version}")
                return latest_version

            # 备用方法：解析PortableApps页面
            return self._parse_portableapps_version()

        except Exception as e:
            self.logger.error(f"检查最新版本失败: {e}")
            # 尝试备用方法
            return self._parse_portableapps_version()

    def _parse_portableapps_version(self) -> Optional[str]:
        """解析PortableApps页面获取版本信息"""
        try:
            url = "https://portableapps.com/apps/internet/google_chrome_portable"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status()

            content = response.text

            # 查找版本信息的模式
            import re
            patterns = [
                r'Google Chrome Portable (\d+\.\d+\.\d+\.\d+)',
                r'Version (\d+\.\d+\.\d+\.\d+)',
                r'chrome-(\d+\.\d+\.\d+\.\d+)',
            ]

            for pattern in patterns:
                match = re.search(pattern, content)
                if match:
                    version = match.group(1)
                    self.latest_version = version
                    self.logger.info(f"从PortableApps获取到版本: {version}")
                    return version

            self.logger.warning("无法从PortableApps页面解析版本信息")
            return None

        except Exception as e:
            self.logger.error(f"解析PortableApps版本失败: {e}")
            return None

    def compare_versions(self, current: str, latest: str) -> int:
        """比较版本号

        Returns:
            -1: current < latest (需要更新)
             0: current == latest (无需更新)
             1: current > latest (当前版本更新)
        """
        try:
            def version_tuple(v):
                return tuple(map(int, v.split('.')))

            current_tuple = version_tuple(current)
            latest_tuple = version_tuple(latest)

            if current_tuple < latest_tuple:
                return -1
            elif current_tuple == latest_tuple:
                return 0
            else:
                return 1

        except Exception as e:
            self.logger.error(f"版本比较失败: {e}")
            return 0

    def need_update(self) -> bool:
        """检查是否需要更新"""
        current = self.get_current_version()
        latest = self.check_latest_version()

        if not current or not latest:
            return False

        return self.compare_versions(current, latest) == -1

    def get_download_url(self) -> Optional[str]:
        """获取下载链接"""
        try:
            # 使用多个可能的下载源
            download_urls = [
                # PortableApps官方下载页面
                "https://portableapps.com/downloading/?app=GoogleChromePortable",
                # 直接下载链接（可能需要根据版本调整）
                "https://portableapps.com/redirect/?a=GoogleChromePortable&s=s&d=pa&f=GoogleChromePortable_online.paf.exe",
                # 备用下载源
                "https://downloads.portableapps.com/GoogleChromePortable_online.paf.exe"
            ]

            # 测试哪个链接可用
            for url in download_urls:
                try:
                    response = requests.head(url, timeout=10)
                    if response.status_code == 200:
                        self.logger.info(f"使用下载链接: {url}")
                        return url
                except:
                    continue

            # 如果都不可用，返回第一个作为默认
            self.logger.warning("无法验证下载链接，使用默认链接")
            return download_urls[0]

        except Exception as e:
            self.logger.error(f"获取下载链接失败: {e}")
            return None

    def download_chrome(self, progress_callback: Optional[Callable] = None) -> bool:
        """下载Chrome Portable"""
        try:
            self.download_status = "downloading"
            self.download_callback = progress_callback

            download_url = self.get_download_url()
            if not download_url:
                raise Exception("无法获取下载链接")

            # 创建临时下载文件
            download_file = self.temp_dir / "GoogleChromePortable.paf.exe"

            self._notify_progress(0, "开始下载Chrome Portable...")

            # 下载文件
            success = self._download_file(download_url, download_file, progress_callback)

            if success and download_file.exists():
                self.logger.info(f"Chrome Portable下载完成: {download_file}")
                return True
            else:
                raise Exception("下载失败")

        except Exception as e:
            self.logger.error(f"下载Chrome失败: {e}")
            self.download_status = "error"
            self._notify_progress(0, f"下载失败: {e}")
            return False

    def _download_file(self, url: str, file_path: Path, progress_callback: Optional[Callable] = None) -> bool:
        """下载文件"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(url, headers=headers, stream=True, timeout=30)
            response.raise_for_status()

            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0

            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)

                        if total_size > 0:
                            progress = int((downloaded_size / total_size) * 100)
                            self.download_progress = progress
                            self._notify_progress(progress, f"下载中... {downloaded_size // 1024 // 1024}MB / {total_size // 1024 // 1024}MB")

            return True

        except Exception as e:
            self.logger.error(f"文件下载失败: {e}")
            return False

    def _notify_progress(self, progress: int, message: str):
        """通知进度更新"""
        self.download_progress = progress
        if self.download_callback:
            self.download_callback(progress, message)

    def backup_current_chrome(self) -> bool:
        """备份当前Chrome"""
        try:
            if not self.chrome_dir.exists():
                self.logger.warning("Chrome目录不存在，无需备份")
                return True

            # 创建备份目录名（包含版本和时间戳）
            current_version = self.get_current_version() or "unknown"
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            backup_name = f"Chrome_{current_version}_{timestamp}"
            backup_path = self.backup_dir / backup_name

            self._notify_progress(0, "备份当前Chrome...")

            # 复制Chrome目录
            shutil.copytree(self.chrome_dir, backup_path)

            self.logger.info(f"Chrome备份完成: {backup_path}")

            # 清理旧备份（保留最近5个）
            self._cleanup_old_backups()

            return True

        except Exception as e:
            self.logger.error(f"备份Chrome失败: {e}")
            return False

    def _cleanup_old_backups(self):
        """清理旧备份"""
        try:
            if not self.backup_dir.exists():
                return

            # 获取所有备份目录
            backups = [d for d in self.backup_dir.iterdir() if d.is_dir() and d.name.startswith("Chrome_")]

            # 按修改时间排序，保留最新的5个
            backups.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            for backup in backups[5:]:  # 删除第6个及以后的备份
                shutil.rmtree(backup)
                self.logger.info(f"删除旧备份: {backup}")

        except Exception as e:
            self.logger.error(f"清理旧备份失败: {e}")

    def install_chrome(self, installer_path: Path) -> bool:
        """安装Chrome Portable"""
        try:
            self.download_status = "installing"
            self._notify_progress(0, "安装Chrome Portable...")

            # 备份当前版本
            if not self.backup_current_chrome():
                raise Exception("备份失败")

            # 删除旧的Chrome目录
            if self.chrome_dir.exists():
                shutil.rmtree(self.chrome_dir)

            # 运行安装程序（PortableApps格式）
            # 注意：这里需要根据实际的安装程序格式调整
            self._notify_progress(50, "解压安装文件...")

            # 如果是.paf.exe文件，需要特殊处理
            if installer_path.suffix.lower() == '.exe':
                # 尝试静默安装
                import subprocess
                result = subprocess.run([str(installer_path), '/S', f'/D={self.chrome_dir}'],
                                      capture_output=True, text=True)

                if result.returncode != 0:
                    # 如果静默安装失败，尝试其他方法
                    self.logger.warning("静默安装失败，尝试手动解压")
                    return self._extract_paf_file(installer_path)

            self._notify_progress(100, "安装完成")
            self.download_status = "completed"

            # 验证安装
            if self.chrome_dir.exists():
                new_version = self.get_current_version()
                self.logger.info(f"Chrome更新完成，新版本: {new_version}")
                return True
            else:
                raise Exception("安装验证失败")

        except Exception as e:
            self.logger.error(f"安装Chrome失败: {e}")
            self.download_status = "error"
            self._notify_progress(0, f"安装失败: {e}")
            return False

    def _extract_paf_file(self, paf_file: Path) -> bool:
        """解压PAF文件"""
        try:
            # PAF文件实际上是一个自解压的ZIP文件
            # 尝试用zipfile解压
            with zipfile.ZipFile(paf_file, 'r') as zip_ref:
                zip_ref.extractall(self.temp_dir / "extracted")

            # 查找Chrome目录
            extracted_dir = self.temp_dir / "extracted"
            chrome_source = None

            for item in extracted_dir.rglob("GoogleChromePortable"):
                if item.is_dir():
                    chrome_source = item
                    break

            if chrome_source:
                # 移动到目标位置
                shutil.move(str(chrome_source), str(self.chrome_dir))
                return True
            else:
                self.logger.error("在解压文件中未找到Chrome目录")
                return False

        except Exception as e:
            self.logger.error(f"解压PAF文件失败: {e}")
            return False

    def update_chrome(self, progress_callback: Optional[Callable] = None) -> bool:
        """完整的Chrome更新流程"""
        try:
            self.download_callback = progress_callback

            # 检查是否需要更新
            if not self.need_update():
                self._notify_progress(100, "Chrome已是最新版本")
                return True

            # 下载新版本
            if not self.download_chrome(progress_callback):
                return False

            # 安装新版本
            download_file = self.temp_dir / "GoogleChromePortable.paf.exe"
            if not self.install_chrome(download_file):
                return False

            # 清理临时文件
            self._cleanup_temp_files()

            self._notify_progress(100, "Chrome更新完成")
            return True

        except Exception as e:
            self.logger.error(f"Chrome更新失败: {e}")
            self.download_status = "error"
            self._notify_progress(0, f"更新失败: {e}")
            return False

    def _cleanup_temp_files(self):
        """清理临时文件"""
        try:
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                self.temp_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info("临时文件清理完成")
        except Exception as e:
            self.logger.error(f"清理临时文件失败: {e}")

    def get_update_info(self) -> Dict[str, Any]:
        """获取更新信息"""
        current = self.get_current_version()
        latest = self.check_latest_version()

        return {
            "current_version": current,
            "latest_version": latest,
            "need_update": self.compare_versions(current or "0.0.0.0", latest or "0.0.0.0") == -1,
            "status": self.download_status,
            "progress": self.download_progress
        }

# 全局Chrome更新管理器实例
chrome_updater = ChromeUpdater()

if __name__ == "__main__":
    # 测试Chrome更新管理器
    print("🔄 Chrome更新管理器测试")

    updater = ChromeUpdater()

    print(f"当前版本: {updater.get_current_version()}")
    print(f"最新版本: {updater.check_latest_version()}")
    print(f"需要更新: {updater.need_update()}")

    print("✅ Chrome更新管理器测试完成")
