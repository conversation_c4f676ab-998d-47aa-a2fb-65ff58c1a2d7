#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - 主题管理器
GUI主题应用和样式管理，支持现代蓝色主题和深色主题

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path

# 导入配置管理器
try:
    from 配置管理器 import config_manager, get_config, set_config
except ImportError:
    print("❌ 无法导入配置管理器，请确保配置管理器.py文件存在")
    exit(1)

class ThemeManager:
    """主题管理器 - GUI主题和样式管理"""

    def __init__(self):
        """初始化主题管理器"""
        self.config = config_manager
        self.current_theme = get_config('system_settings.theme', 'modern_blue')
        self.themes = self._define_themes()
        self._setup_logging()

        self.logger.info("主题管理器初始化完成")

    def _setup_logging(self):
        """设置日志系统"""
        log_dir = self.config.get_project_root() / "日志"
        log_dir.mkdir(exist_ok=True)

        self.logger = logging.getLogger("ThemeManager")
        if not self.logger.handlers:
            handler = logging.FileHandler(log_dir / "主题管理器.log", encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

    def _define_themes(self) -> Dict[str, Dict[str, Any]]:
        """定义主题配色方案"""
        themes = {
            "modern_blue": {
                "name": "现代蓝色主题",
                "description": "专业的蓝白配色，适合日常使用",
                "colors": {
                    # 主要颜色
                    "primary": "#2196F3",      # 主蓝色
                    "primary_dark": "#1976D2", # 深蓝色
                    "primary_light": "#BBDEFB", # 浅蓝色

                    # 背景颜色
                    "bg_primary": "#FFFFFF",    # 主背景
                    "bg_secondary": "#F5F5F5",  # 次背景
                    "bg_tertiary": "#E3F2FD",   # 第三背景

                    # 文本颜色
                    "text_primary": "#212121",   # 主文本
                    "text_secondary": "#757575", # 次文本
                    "text_hint": "#BDBDBD",     # 提示文本

                    # 边框和分割线
                    "border": "#E0E0E0",        # 边框
                    "divider": "#EEEEEE",       # 分割线

                    # 状态颜色
                    "success": "#4CAF50",       # 成功
                    "warning": "#FF9800",       # 警告
                    "error": "#F44336",         # 错误
                    "info": "#2196F3",          # 信息

                    # 按钮颜色
                    "btn_primary": "#2196F3",   # 主按钮
                    "btn_secondary": "#757575", # 次按钮
                    "btn_hover": "#1976D2",     # 悬停
                    "btn_active": "#0D47A1",    # 激活
                }
            },

            "dark_theme": {
                "name": "深色主题",
                "description": "护眼的深色配色，减少视觉疲劳",
                "colors": {
                    # 主要颜色
                    "primary": "#64B5F6",      # 主蓝色（亮）
                    "primary_dark": "#42A5F5", # 深蓝色
                    "primary_light": "#90CAF9", # 浅蓝色

                    # 背景颜色
                    "bg_primary": "#121212",    # 主背景（深黑）
                    "bg_secondary": "#1E1E1E",  # 次背景（深灰）
                    "bg_tertiary": "#2D2D2D",   # 第三背景（中灰）

                    # 文本颜色
                    "text_primary": "#FFFFFF",   # 主文本（白）
                    "text_secondary": "#B0B0B0", # 次文本（浅灰）
                    "text_hint": "#757575",     # 提示文本（中灰）

                    # 边框和分割线
                    "border": "#404040",        # 边框（深灰）
                    "divider": "#303030",       # 分割线（更深灰）

                    # 状态颜色
                    "success": "#66BB6A",       # 成功（绿）
                    "warning": "#FFA726",       # 警告（橙）
                    "error": "#EF5350",         # 错误（红）
                    "info": "#64B5F6",          # 信息（蓝）

                    # 按钮颜色
                    "btn_primary": "#64B5F6",   # 主按钮
                    "btn_secondary": "#757575", # 次按钮
                    "btn_hover": "#42A5F5",     # 悬停
                    "btn_active": "#1E88E5",    # 激活
                }
            }
        }

        return themes

    def get_current_theme(self) -> str:
        """获取当前主题名称"""
        return self.current_theme

    def get_theme_info(self, theme_name: str) -> Optional[Dict[str, Any]]:
        """获取主题信息"""
        return self.themes.get(theme_name)

    def get_available_themes(self) -> Dict[str, str]:
        """获取可用主题列表"""
        return {name: info["name"] for name, info in self.themes.items()}

    def get_color(self, color_key: str, theme_name: Optional[str] = None) -> str:
        """
        获取主题颜色

        Args:
            color_key: 颜色键，如 'primary', 'bg_primary'
            theme_name: 主题名称，默认使用当前主题

        Returns:
            颜色值（十六进制）
        """
        if theme_name is None:
            theme_name = self.current_theme

        theme = self.themes.get(theme_name)
        if not theme:
            self.logger.warning(f"主题 {theme_name} 不存在，使用默认主题")
            theme = self.themes["modern_blue"]

        color = theme["colors"].get(color_key)
        if not color:
            self.logger.warning(f"颜色键 {color_key} 不存在，使用默认颜色")
            return "#000000"  # 默认黑色

        return color

    def apply_theme_to_widget(self, widget, widget_type: str = "default"):
        """
        应用主题到tkinter组件

        Args:
            widget: tkinter组件
            widget_type: 组件类型（button, label, frame, entry等）
        """
        try:
            # 获取组件支持的选项
            widget_options = widget.keys()

            if widget_type == "button":
                config_dict = {}
                if 'bg' in widget_options:
                    config_dict['bg'] = self.get_color("btn_primary")
                if 'fg' in widget_options:
                    # 按钮文字颜色：深色主题用白色，浅色主题用白色（保持对比）
                    if self.current_theme == "dark_theme":
                        config_dict['fg'] = "#FFFFFF"
                    else:
                        config_dict['fg'] = "#FFFFFF"
                if 'activebackground' in widget_options:
                    config_dict['activebackground'] = self.get_color("btn_hover")
                if 'activeforeground' in widget_options:
                    config_dict['activeforeground'] = "#FFFFFF"
                if 'relief' in widget_options:
                    config_dict['relief'] = "flat"
                if 'borderwidth' in widget_options:
                    config_dict['borderwidth'] = 0
                if 'font' in widget_options:
                    config_dict['font'] = ("Microsoft YaHei", 9)
                widget.configure(**config_dict)

            elif widget_type == "label":
                config_dict = {}
                if 'bg' in widget_options:
                    config_dict['bg'] = self.get_color("bg_primary")
                if 'fg' in widget_options:
                    config_dict['fg'] = self.get_color("text_primary")
                if 'font' in widget_options:
                    config_dict['font'] = ("Microsoft YaHei", 9)
                widget.configure(**config_dict)

            elif widget_type == "frame":
                config_dict = {}
                if 'bg' in widget_options:
                    config_dict['bg'] = self.get_color("bg_primary")
                if 'relief' in widget_options:
                    config_dict['relief'] = "flat"
                if 'borderwidth' in widget_options:
                    config_dict['borderwidth'] = 0
                widget.configure(**config_dict)

            elif widget_type == "entry":
                config_dict = {}
                if 'bg' in widget_options:
                    config_dict['bg'] = self.get_color("bg_secondary")
                if 'fg' in widget_options:
                    config_dict['fg'] = self.get_color("text_primary")
                if 'insertbackground' in widget_options:
                    config_dict['insertbackground'] = self.get_color("text_primary")
                if 'relief' in widget_options:
                    config_dict['relief'] = "solid"
                if 'borderwidth' in widget_options:
                    config_dict['borderwidth'] = 1
                if 'font' in widget_options:
                    config_dict['font'] = ("Microsoft YaHei", 9)
                widget.configure(**config_dict)

            elif widget_type == "text":
                config_dict = {}
                if 'bg' in widget_options:
                    config_dict['bg'] = self.get_color("bg_secondary")
                if 'fg' in widget_options:
                    config_dict['fg'] = self.get_color("text_primary")
                if 'insertbackground' in widget_options:
                    config_dict['insertbackground'] = self.get_color("text_primary")
                if 'selectbackground' in widget_options:
                    config_dict['selectbackground'] = self.get_color("primary_light")
                if 'relief' in widget_options:
                    config_dict['relief'] = "solid"
                if 'borderwidth' in widget_options:
                    config_dict['borderwidth'] = 1
                if 'font' in widget_options:
                    config_dict['font'] = ("Microsoft YaHei", 9)
                widget.configure(**config_dict)

            elif widget_type == "listbox":
                config_dict = {}
                if 'bg' in widget_options:
                    config_dict['bg'] = self.get_color("bg_secondary")
                if 'fg' in widget_options:
                    config_dict['fg'] = self.get_color("text_primary")
                if 'selectbackground' in widget_options:
                    config_dict['selectbackground'] = self.get_color("primary")
                if 'selectforeground' in widget_options:
                    # 选中项文字颜色：确保在选中背景上可见
                    if self.current_theme == "dark_theme":
                        config_dict['selectforeground'] = "#FFFFFF"
                    else:
                        config_dict['selectforeground'] = "#FFFFFF"
                if 'relief' in widget_options:
                    config_dict['relief'] = "solid"
                if 'borderwidth' in widget_options:
                    config_dict['borderwidth'] = 1
                if 'font' in widget_options:
                    config_dict['font'] = ("Microsoft YaHei", 9)
                widget.configure(**config_dict)

            elif widget_type == "window":
                if 'bg' in widget_options:
                    widget.configure(bg=self.get_color("bg_primary"))

            else:  # default
                config_dict = {}
                if 'bg' in widget_options:
                    config_dict['bg'] = self.get_color("bg_primary")
                if 'fg' in widget_options:
                    config_dict['fg'] = self.get_color("text_primary")
                if config_dict:
                    widget.configure(**config_dict)

        except Exception as e:
            self.logger.error(f"应用主题到组件失败: {e}")

    def apply_theme_to_window(self, window):
        """应用主题到整个窗口"""
        try:
            # 设置窗口背景
            window.configure(bg=self.get_color("bg_primary"))

            # 递归应用主题到所有子组件
            self._apply_theme_recursive(window)

            # 强制刷新窗口
            window.update_idletasks()
            window.update()

            self.logger.info(f"主题 {self.current_theme} 应用到窗口成功")

        except Exception as e:
            self.logger.error(f"应用主题到窗口失败: {e}")

    def force_refresh_theme(self, window):
        """强制刷新主题（用于解决主题切换后不生效的问题）"""
        try:
            # 先清除所有样式
            self._clear_widget_styles(window)

            # 重新应用主题
            self.apply_theme_to_window(window)

            self.logger.info(f"强制刷新主题 {self.current_theme} 完成")

        except Exception as e:
            self.logger.error(f"强制刷新主题失败: {e}")

    def _clear_widget_styles(self, widget):
        """清除组件样式（递归）"""
        try:
            # 重置为默认样式
            widget_class = widget.winfo_class()
            if widget_class in ["Button", "Label", "Frame", "Entry", "Text", "Listbox"]:
                # 获取组件支持的选项
                widget_options = widget.keys()
                reset_dict = {}

                if 'bg' in widget_options:
                    reset_dict['bg'] = "SystemButtonFace"  # 系统默认背景
                if 'fg' in widget_options:
                    reset_dict['fg'] = "SystemButtonText"  # 系统默认文字

                if reset_dict:
                    widget.configure(**reset_dict)

            # 递归处理子组件
            for child in widget.winfo_children():
                self._clear_widget_styles(child)

        except Exception:
            pass  # 忽略错误，继续处理

    def _apply_theme_recursive(self, widget):
        """递归应用主题到所有子组件"""
        try:
            # 根据组件类型应用主题
            widget_class = widget.winfo_class()

            if widget_class == "Button":
                self.apply_theme_to_widget(widget, "button")
            elif widget_class == "Label":
                self.apply_theme_to_widget(widget, "label")
            elif widget_class == "Frame":
                self.apply_theme_to_widget(widget, "frame")
            elif widget_class == "Entry":
                self.apply_theme_to_widget(widget, "entry")
            elif widget_class == "Text":
                self.apply_theme_to_widget(widget, "text")
            elif widget_class == "Listbox":
                self.apply_theme_to_widget(widget, "listbox")
            else:
                self.apply_theme_to_widget(widget, "default")

            # 递归处理子组件
            for child in widget.winfo_children():
                self._apply_theme_recursive(child)

        except Exception as e:
            self.logger.error(f"递归应用主题失败: {e}")

    def switch_theme(self, theme_name: str) -> bool:
        """
        切换主题

        Args:
            theme_name: 目标主题名称

        Returns:
            是否切换成功
        """
        try:
            if theme_name not in self.themes:
                self.logger.error(f"主题 {theme_name} 不存在")
                return False

            # 更新当前主题
            old_theme = self.current_theme
            self.current_theme = theme_name

            # 保存到配置文件
            if set_config('system_settings.theme', theme_name):
                self.logger.info(f"主题从 {old_theme} 切换到 {theme_name}")
                return True
            else:
                # 回滚
                self.current_theme = old_theme
                self.logger.error("保存主题配置失败")
                return False

        except Exception as e:
            self.logger.error(f"切换主题失败: {e}")
            return False

# 全局主题管理器实例
theme_manager = ThemeManager()

def get_theme_color(color_key: str) -> str:
    """快捷函数：获取当前主题颜色"""
    return theme_manager.get_color(color_key)

def apply_theme(widget, widget_type: str = "default"):
    """快捷函数：应用主题到组件"""
    theme_manager.apply_theme_to_widget(widget, widget_type)

def switch_theme(theme_name: str) -> bool:
    """快捷函数：切换主题"""
    return theme_manager.switch_theme(theme_name)

if __name__ == "__main__":
    # 测试主题管理器
    print("🎨 主题管理器测试")
    print(f"当前主题: {theme_manager.get_current_theme()}")
    print(f"可用主题: {theme_manager.get_available_themes()}")
    print(f"主色调: {get_theme_color('primary')}")
    print(f"背景色: {get_theme_color('bg_primary')}")
    print("✅ 主题管理器测试完成")
