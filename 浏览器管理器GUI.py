#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - 图形界面主程序
现代化的GUI界面，支持主题切换、多语言、完整的浏览器管理功能

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sys
import threading
from pathlib import Path
from typing import List, Dict, Optional

# 导入核心模块
try:
    from 配置管理器 import config_manager, get_config, set_config
    from 主题管理器 import theme_manager, apply_theme, switch_theme, get_theme_color
    from 浏览器管理器 import BrowserManager
    from 快捷方式管理器 import shortcut_manager, create_desktop_shortcut
except ImportError as e:
    print(f"❌ 导入核心模块失败: {e}")
    sys.exit(1)

class BrowserManagerGUI:
    """浏览器管理器图形界面"""

    def __init__(self):
        """初始化GUI应用"""
        self.root = tk.Tk()
        self.browser_manager = BrowserManager()
        self.browsers_data = []
        self.themed_widgets = []  # 初始化主题组件列表

        # 添加主题管理器引用
        self.theme_manager = theme_manager

        self.setup_window()
        self.setup_ui()
        self.apply_theme()
        self.refresh_browser_list()

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_window(self):
        """设置主窗口"""
        self.root.title("🌟 浏览器多账号绿色版 v2.2.1")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)

        # 设置窗口图标（如果有的话）
        try:
            icon_path = config_manager.get_icons_dir() / "chrome.png"
            if icon_path.exists():
                # 注意：tkinter需要.ico文件作为图标
                pass
        except:
            pass

    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        main_container = tk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 顶部工具栏
        self.setup_toolbar(main_container)

        # 中间内容区域
        content_frame = tk.Frame(main_container)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # 左侧浏览器列表
        self.setup_browser_list(content_frame)

        # 右侧操作面板
        self.setup_action_panel(content_frame)

        # 底部状态栏
        self.setup_status_bar(main_container)

        # 添加主容器到主题组件列表
        self.themed_widgets.extend([
            (main_container, "frame"),
            (content_frame, "frame")
        ])

    def setup_toolbar(self, parent):
        """设置工具栏"""
        toolbar = tk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        # 左侧按钮组
        left_buttons = tk.Frame(toolbar)
        left_buttons.pack(side=tk.LEFT)

        # 主要功能按钮
        self.create_btn = tk.Button(left_buttons, text="🆕 创建浏览器",
                                   command=self.create_browser, width=12)
        self.create_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.launch_btn = tk.Button(left_buttons, text="🚀 启动浏览器",
                                   command=self.launch_browser, width=12)
        self.launch_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.rename_btn = tk.Button(left_buttons, text="✏️ 重命名",
                                   command=self.rename_browser, width=10)
        self.rename_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.desktop_btn = tk.Button(left_buttons, text="🖥️ 发送桌面",
                                    command=self.send_to_desktop, width=12)
        self.desktop_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.refresh_btn = tk.Button(left_buttons, text="🔄 刷新",
                                    command=self.refresh_browser_list, width=8)
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 右侧设置按钮组
        right_buttons = tk.Frame(toolbar)
        right_buttons.pack(side=tk.RIGHT)

        # 主题切换按钮
        self.theme_btn = tk.Button(right_buttons, text="🎨 主题",
                                  command=self.show_theme_dialog, width=8)
        self.theme_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # 设置按钮
        self.settings_btn = tk.Button(right_buttons, text="⚙️ 设置",
                                     command=self.show_settings, width=8)
        self.settings_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # 添加到主题组件列表
        self.themed_widgets.extend([
            (toolbar, "frame"),
            (left_buttons, "frame"),
            (right_buttons, "frame"),
            (self.create_btn, "button"),
            (self.launch_btn, "button"),
            (self.rename_btn, "button"),
            (self.desktop_btn, "button"),
            (self.refresh_btn, "button"),
            (self.theme_btn, "button"),
            (self.settings_btn, "button")
        ])

    def setup_browser_list(self, parent):
        """设置浏览器列表"""
        # 左侧面板
        left_panel = tk.Frame(parent)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # 列表标题
        list_title = tk.Label(left_panel, text="📋 浏览器实例列表",
                             font=("Microsoft YaHei", 12, "bold"))
        list_title.pack(anchor=tk.W, pady=(0, 10))

        # 列表框架
        list_frame = tk.Frame(left_panel)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # 浏览器列表
        self.browser_listbox = tk.Listbox(list_frame, font=("Microsoft YaHei", 10))
        self.browser_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 滚动条
        scrollbar = tk.Scrollbar(list_frame, orient=tk.VERTICAL)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定滚动条
        self.browser_listbox.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.browser_listbox.yview)

        # 绑定双击事件
        self.browser_listbox.bind("<Double-Button-1>", self.on_browser_double_click)

        # 绑定右键菜单
        self.browser_listbox.bind("<Button-3>", self.show_context_menu)

        # 添加到主题组件列表
        self.themed_widgets.extend([
            (left_panel, "frame"),
            (list_title, "label"),
            (list_frame, "frame"),
            (self.browser_listbox, "listbox")
        ])

    def setup_action_panel(self, parent):
        """设置操作面板"""
        # 右侧面板
        right_panel = tk.Frame(parent, width=300)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y)
        right_panel.pack_propagate(False)

        # 操作面板标题
        panel_title = tk.Label(right_panel, text="🎯 操作面板",
                              font=("Microsoft YaHei", 12, "bold"))
        panel_title.pack(anchor=tk.W, pady=(0, 15))

        # 浏览器信息区域
        info_frame = tk.LabelFrame(right_panel, text="浏览器信息",
                                  font=("Microsoft YaHei", 10))
        info_frame.pack(fill=tk.X, pady=(0, 15))

        # 浏览器名称
        name_frame = tk.Frame(info_frame)
        name_frame.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(name_frame, text="名称:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT)
        self.name_label = tk.Label(name_frame, text="未选择",
                                  font=("Microsoft YaHei", 9, "bold"))
        self.name_label.pack(side=tk.LEFT, padx=(5, 0))

        # 浏览器状态
        status_frame = tk.Frame(info_frame)
        status_frame.pack(fill=tk.X, padx=10, pady=5)
        tk.Label(status_frame, text="状态:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT)
        self.status_label = tk.Label(status_frame, text="未知",
                                    font=("Microsoft YaHei", 9))
        self.status_label.pack(side=tk.LEFT, padx=(5, 0))

        # 操作按钮区域
        action_frame = tk.LabelFrame(right_panel, text="操作",
                                    font=("Microsoft YaHei", 10))
        action_frame.pack(fill=tk.X, pady=(0, 15))

        # 操作按钮
        buttons = [
            ("🚀 启动浏览器", self.launch_selected_browser),
            ("✏️ 重命名", self.rename_browser),
            ("🖥️ 发送到桌面", self.send_selected_to_desktop),
            ("🎨 设置图标", self.set_browser_icon),
            ("🗑️ 删除浏览器", self.delete_browser)
        ]

        self.action_buttons = []
        for text, command in buttons:
            btn = tk.Button(action_frame, text=text, command=command,
                           width=20, state=tk.DISABLED)
            btn.pack(pady=5, padx=10)
            self.action_buttons.append(btn)

        # 高级功能区域
        advanced_frame = tk.LabelFrame(right_panel, text="高级功能",
                                      font=("Microsoft YaHei", 10))
        advanced_frame.pack(fill=tk.X)

        # 高级功能按钮
        advanced_buttons = [
            ("🔄 同步插件", self.sync_plugins),
            ("📥 图标下载", self.download_icons),
            ("🔧 系统设置", self.show_settings)
        ]

        for text, command in advanced_buttons:
            btn = tk.Button(advanced_frame, text=text, command=command, width=20)
            btn.pack(pady=5, padx=10)

        # 添加到主题组件列表
        self.themed_widgets.extend([
            (right_panel, "frame"),
            (panel_title, "label"),
            (info_frame, "frame"),
            (name_frame, "frame"),
            (status_frame, "frame"),
            (self.name_label, "label"),
            (self.status_label, "label"),
            (action_frame, "frame"),
            (advanced_frame, "frame")
        ])

        # 添加按钮到主题列表
        for btn in self.action_buttons:
            self.themed_widgets.append((btn, "button"))

    def setup_status_bar(self, parent):
        """设置状态栏"""
        status_bar = tk.Frame(parent)
        status_bar.pack(fill=tk.X, pady=(10, 0))

        # 状态信息
        self.status_text = tk.Label(status_bar, text="就绪",
                                   font=("Microsoft YaHei", 9))
        self.status_text.pack(side=tk.LEFT)

        # 版本信息
        version_label = tk.Label(status_bar, text="v2.2.1 | CogniGraph™",
                                font=("Microsoft YaHei", 8))
        version_label.pack(side=tk.RIGHT)

        # 添加到主题组件列表
        self.themed_widgets.extend([
            (status_bar, "frame"),
            (self.status_text, "label"),
            (version_label, "label")
        ])

    def apply_theme(self):
        """应用当前主题"""
        try:
            # 应用窗口主题
            theme_manager.apply_theme_to_window(self.root)

            # 应用到所有组件
            for widget, widget_type in self.themed_widgets:
                apply_theme(widget, widget_type)

            # 强制刷新
            self.root.update()

        except Exception as e:
            print(f"❌ 应用主题失败: {e}")

    def refresh_browser_list(self):
        """刷新浏览器列表"""
        try:
            # 获取浏览器列表
            self.browsers_data = self.browser_manager.list_browsers()

            # 清空列表
            self.browser_listbox.delete(0, tk.END)

            # 添加浏览器到列表
            for browser in self.browsers_data:
                status_icon = "✅" if browser['status'] == "正常" else "❌"
                display_text = f"{status_icon} {browser['name']}"
                self.browser_listbox.insert(tk.END, display_text)

            # 更新状态
            count = len(self.browsers_data)
            self.update_status(f"找到 {count} 个浏览器实例")

            # 清空选择信息
            self.clear_selection_info()

        except Exception as e:
            self.update_status(f"刷新列表失败: {e}")
            messagebox.showerror("错误", f"刷新浏览器列表失败: {e}")

    def update_status(self, message: str):
        """更新状态栏"""
        self.status_text.config(text=message)
        # 3秒后恢复默认状态
        self.root.after(3000, lambda: self.status_text.config(text="就绪"))

    def clear_selection_info(self):
        """清空选择信息"""
        self.name_label.config(text="未选择")
        self.status_label.config(text="未知")

        # 禁用操作按钮
        for btn in self.action_buttons:
            btn.config(state=tk.DISABLED)

    def get_selected_browser(self) -> Optional[Dict]:
        """获取选中的浏览器"""
        selection = self.browser_listbox.curselection()
        if not selection:
            return None

        index = selection[0]
        if 0 <= index < len(self.browsers_data):
            return self.browsers_data[index]

        return None

    def on_browser_selection_change(self, event=None):
        """浏览器选择改变事件"""
        browser = self.get_selected_browser()
        if browser:
            self.name_label.config(text=browser['name'])
            self.status_label.config(text=browser['status'])

            # 启用操作按钮
            for btn in self.action_buttons:
                btn.config(state=tk.NORMAL)
        else:
            self.clear_selection_info()

    def on_browser_double_click(self, event):
        """浏览器双击事件"""
        self.launch_browser()

    def create_browser(self):
        """创建新浏览器"""
        # 这里会在后续实现完整的创建对话框
        name = simpledialog.askstring("创建浏览器", "请输入浏览器名称:")
        if name and name.strip():
            if self.browser_manager.create_browser(name.strip()):
                self.update_status(f"浏览器 '{name}' 创建成功")
                self.refresh_browser_list()
            else:
                messagebox.showerror("错误", f"创建浏览器 '{name}' 失败")

    def launch_browser(self):
        """启动浏览器"""
        browser = self.get_selected_browser()
        if not browser:
            messagebox.showwarning("提示", "请先选择一个浏览器")
            return

        if self.browser_manager.launch_browser(browser['name']):
            self.update_status(f"浏览器 '{browser['name']}' 启动成功")
        else:
            messagebox.showerror("错误", f"启动浏览器 '{browser['name']}' 失败")

    def launch_selected_browser(self):
        """启动选中的浏览器"""
        self.launch_browser()

    def send_to_desktop(self):
        """发送到桌面"""
        browser = self.get_selected_browser()
        if not browser:
            messagebox.showwarning("提示", "请先选择一个浏览器")
            return

        if create_desktop_shortcut(browser['name']):
            self.update_status(f"'{browser['name']}' 桌面快捷方式创建成功")
        else:
            messagebox.showerror("错误", "创建桌面快捷方式失败")

    def send_selected_to_desktop(self):
        """发送选中的浏览器到桌面"""
        self.send_to_desktop()

    def rename_browser(self):
        """重命名浏览器"""
        browser = self.get_selected_browser()
        if not browser:
            messagebox.showwarning("提示", "请先选择一个浏览器")
            return

        self.show_rename_dialog(browser['name'])

    def show_rename_dialog(self, current_name: str = None):
        """显示重命名对话框"""
        if current_name is None:
            browser = self.get_selected_browser()
            if not browser:
                messagebox.showwarning("提示", "请先选择一个浏览器")
                return
            current_name = browser['name']

        # 创建重命名对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("重命名浏览器")
        dialog.geometry("500x280")
        dialog.resizable(True, False)  # 允许水平调整大小
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # 应用主题
        self.theme_manager.apply_theme_to_window(dialog)

        # 主框架
        main_frame = tk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=25, pady=25)

        # 标题
        title_label = tk.Label(main_frame, text="🔄 重命名浏览器",
                              font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(pady=(0, 25))

        # 当前名称显示
        current_frame = tk.Frame(main_frame)
        current_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(current_frame, text="当前名称:", font=("Microsoft YaHei", 12)).pack(anchor=tk.W)
        current_label = tk.Label(current_frame, text=current_name,
                                font=("Microsoft YaHei", 12, "bold"),
                                fg="#2196F3")
        current_label.pack(anchor=tk.W, pady=(8, 0))

        # 新名称输入
        new_frame = tk.Frame(main_frame)
        new_frame.pack(fill=tk.X, pady=(0, 25))

        tk.Label(new_frame, text="新名称:", font=("Microsoft YaHei", 12)).pack(anchor=tk.W)
        name_entry = tk.Entry(new_frame, font=("Microsoft YaHei", 14), width=35)
        name_entry.pack(fill=tk.X, pady=(8, 0), ipady=8)  # 增加内边距让输入框更高
        name_entry.insert(0, current_name)
        name_entry.select_range(0, tk.END)
        name_entry.focus()

        # 按钮框架
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        def on_rename():
            new_name = name_entry.get().strip()

            if not new_name:
                messagebox.showerror("错误", "新名称不能为空")
                return

            if new_name == current_name:
                messagebox.showwarning("提示", "新名称与当前名称相同")
                return

            # 验证名称有效性
            if not self.browser_manager._validate_browser_name(new_name):
                messagebox.showerror("错误", "浏览器名称无效\n名称不能包含特殊字符: < > : \" | ? * \\")
                return

            # 执行重命名
            if self.browser_manager.rename_browser(current_name, new_name):
                self.update_status(f"浏览器重命名成功: {current_name} → {new_name}")
                self.refresh_browser_list()
                dialog.destroy()
            else:
                messagebox.showerror("错误", "重命名失败\n可能原因:\n- 新名称已存在\n- 浏览器正在运行\n- 文件系统权限不足")

        def on_cancel():
            dialog.destroy()

        # 按钮
        cancel_btn = tk.Button(button_frame, text="取消", command=on_cancel,
                              width=12, font=("Microsoft YaHei", 11))
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))

        rename_btn = tk.Button(button_frame, text="重命名", command=on_rename,
                              width=12, font=("Microsoft YaHei", 11))
        rename_btn.pack(side=tk.RIGHT)

        # 绑定回车键
        dialog.bind('<Return>', lambda e: on_rename())
        dialog.bind('<Escape>', lambda e: on_cancel())

        # 添加到主题组件列表
        themed_widgets = [
            (dialog, "window"),
            (main_frame, "frame"),
            (title_label, "label"),
            (current_frame, "frame"),
            (current_label, "label"),
            (new_frame, "frame"),
            (name_entry, "entry"),
            (button_frame, "frame"),
            (cancel_btn, "button"),
            (rename_btn, "button")
        ]

        for widget, widget_type in themed_widgets:
            self.theme_manager.apply_theme_to_widget(widget, widget_type)

    def set_browser_icon(self):
        """设置浏览器图标"""
        messagebox.showinfo("提示", "图标设置功能将在后续版本实现")

    def delete_browser(self):
        """删除浏览器"""
        browser = self.get_selected_browser()
        if not browser:
            messagebox.showwarning("提示", "请先选择一个浏览器")
            return

        # 确认删除
        result = messagebox.askyesno("确认删除",
                                   f"确定要删除浏览器 '{browser['name']}' 吗？\n此操作不可恢复！")
        if result:
            if self.browser_manager.delete_browser(browser['name']):
                self.update_status(f"浏览器 '{browser['name']}' 删除成功")
                self.refresh_browser_list()
            else:
                messagebox.showerror("错误", f"删除浏览器 '{browser['name']}' 失败")

    def sync_plugins(self):
        """同步插件"""
        messagebox.showinfo("提示", "插件同步功能将在后续版本实现")

    def download_icons(self):
        """下载图标"""
        messagebox.showinfo("提示", "图标下载功能将在后续版本实现")

    def show_theme_dialog(self):
        """显示主题选择对话框"""
        try:
            # 创建主题选择对话框
            dialog = tk.Toplevel(self.root)
            dialog.title("🎨 主题选择")
            dialog.geometry("400x300")
            dialog.resizable(False, False)
            dialog.transient(self.root)
            dialog.grab_set()

            # 居中显示
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
            y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
            dialog.geometry(f"+{x}+{y}")

            # 应用当前主题到对话框
            self.theme_manager.apply_theme_to_window(dialog)

            # 主框架
            main_frame = tk.Frame(dialog)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # 标题
            title_label = tk.Label(main_frame, text="🎨 选择主题",
                                  font=("Microsoft YaHei", 14, "bold"))
            title_label.pack(pady=(0, 20))

            # 当前主题显示
            current_theme = theme_manager.get_current_theme()
            current_theme_name = theme_manager.get_theme_info(current_theme)['name']
            current_label = tk.Label(main_frame, text=f"当前主题: {current_theme_name}",
                                   font=("Microsoft YaHei", 10))
            current_label.pack(pady=(0, 15))

            # 主题选项
            themes = [
                ("🌞 浅色主题", "modern_blue", "专业的蓝白配色，适合日常使用"),
                ("🌙 深色主题", "dark_theme", "护眼的深色配色，减少视觉疲劳"),
                ("🔄 跟随系统", "auto", "自动跟随系统主题设置")
            ]

            def apply_theme_choice(theme_key):
                """应用主题选择"""
                try:
                    if theme_key == "auto":
                        # 检测系统主题
                        system_theme = self.detect_system_theme()
                        actual_theme = system_theme
                        theme_name = "跟随系统"
                    else:
                        actual_theme = theme_key
                        theme_name = next(name for name, key, _ in themes if key == theme_key)

                    if switch_theme(actual_theme):
                        self.apply_theme()
                        self.update_status(f"已切换到{theme_name}")
                        dialog.destroy()
                    else:
                        messagebox.showerror("错误", "主题切换失败")

                except Exception as e:
                    messagebox.showerror("错误", f"主题切换失败: {e}")

            # 创建主题按钮
            for theme_text, theme_key, theme_desc in themes:
                # 主题按钮框架
                theme_frame = tk.Frame(main_frame)
                theme_frame.pack(fill=tk.X, pady=5)

                # 主题按钮
                theme_btn = tk.Button(theme_frame, text=theme_text,
                                    command=lambda k=theme_key: apply_theme_choice(k),
                                    width=20, font=("Microsoft YaHei", 10))
                theme_btn.pack(side=tk.LEFT)

                # 主题描述
                desc_label = tk.Label(theme_frame, text=theme_desc,
                                    font=("Microsoft YaHei", 8))
                desc_label.pack(side=tk.LEFT, padx=(10, 0))

                # 当前主题标记
                if (theme_key == current_theme) or (theme_key == "auto" and current_theme in ["modern_blue", "dark_theme"]):
                    current_mark = tk.Label(theme_frame, text="✓",
                                          font=("Microsoft YaHei", 12, "bold"))
                    current_mark.pack(side=tk.RIGHT)

                # 应用主题到组件
                self.theme_manager.apply_theme_to_widget(theme_frame, "frame")
                self.theme_manager.apply_theme_to_widget(theme_btn, "button")
                self.theme_manager.apply_theme_to_widget(desc_label, "label")

            # 取消按钮
            cancel_btn = tk.Button(main_frame, text="取消", command=dialog.destroy,
                                 width=15, font=("Microsoft YaHei", 10))
            cancel_btn.pack(pady=(20, 0))

            # 应用主题到对话框组件
            themed_widgets = [
                (main_frame, "frame"),
                (title_label, "label"),
                (current_label, "label"),
                (cancel_btn, "button")
            ]

            for widget, widget_type in themed_widgets:
                self.theme_manager.apply_theme_to_widget(widget, widget_type)

        except Exception as e:
            messagebox.showerror("错误", f"主题对话框打开失败: {e}")

    def detect_system_theme(self):
        """检测系统主题"""
        try:
            import winreg
            registry = winreg.ConnectRegistry(None, winreg.HKEY_CURRENT_USER)
            key = winreg.OpenKey(registry, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize")
            value, _ = winreg.QueryValueEx(key, "AppsUseLightTheme")
            winreg.CloseKey(key)

            if value == 0:  # 深色模式
                return "dark_theme"
            else:  # 浅色模式
                return "modern_blue"

        except Exception:
            # 如果无法检测，默认返回浅色主题
            return "modern_blue"

    def show_settings(self):
        """显示设置"""
        try:
            from 设置对话框 import SettingsDialog

            def on_settings_changed(changed_settings):
                """设置更改回调"""
                # 检查是否有主题相关设置更改
                theme_changed = any("theme" in setting for setting in changed_settings)
                language_changed = any("language" in setting for setting in changed_settings)
                gui_changed = any("gui_settings" in setting for setting in changed_settings)

                if theme_changed:
                    # 重新应用主题
                    self.apply_theme()
                    self.update_status("主题设置已更新")

                if language_changed:
                    self.update_status("语言设置已更新，重启后生效")

                if gui_changed:
                    self.update_status("界面设置已更新")

                # 刷新界面
                self.refresh_browser_list()

            # 创建并显示设置对话框
            dialog = SettingsDialog(self.root, on_settings_changed)

        except ImportError as e:
            messagebox.showerror("错误", f"无法加载设置模块: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"打开设置失败: {e}")

    def show_context_menu(self, event):
        """显示右键菜单"""
        # 选择点击的项目
        index = self.browser_listbox.nearest(event.y)
        self.browser_listbox.selection_clear(0, tk.END)
        self.browser_listbox.selection_set(index)
        self.on_browser_selection_change()

        # 创建右键菜单
        context_menu = tk.Menu(self.root, tearoff=0)
        context_menu.add_command(label="🚀 启动", command=self.launch_browser)
        context_menu.add_command(label="🖥️ 发送到桌面", command=self.send_to_desktop)
        context_menu.add_separator()
        context_menu.add_command(label="✏️ 重命名", command=self.rename_browser)
        context_menu.add_command(label="🎨 设置图标", command=self.set_browser_icon)
        context_menu.add_separator()
        context_menu.add_command(label="🗑️ 删除", command=self.delete_browser)

        # 显示菜单
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def on_closing(self):
        """窗口关闭事件"""
        self.root.destroy()

    def run(self):
        """运行GUI应用"""
        # 绑定选择事件
        self.browser_listbox.bind("<<ListboxSelect>>", self.on_browser_selection_change)

        # 启动主循环
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = BrowserManagerGUI()
        app.run()
    except Exception as e:
        print(f"❌ GUI应用启动失败: {e}")
        messagebox.showerror("错误", f"GUI应用启动失败: {e}")

if __name__ == "__main__":
    main()
