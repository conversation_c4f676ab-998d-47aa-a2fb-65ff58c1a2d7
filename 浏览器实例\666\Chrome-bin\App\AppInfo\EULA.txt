Please note that you MUST sign into Google Chrome with your associated Google account in order to have a fully functional browser when used portably. Unlike other browsers, some components of Google Chrome are locked to a given PC and will be lost if you move from PC to PC including extensions, extension settings and data, homepage and search settings, etc. This is by design of the Google Chrome team.

Passwords are not portable by default but can be made portable using a configuration option within the launcher's INI file. They may be portable while signed into Google.

Certificates and Chrome apps installed outside the browser are not portable and are installed on the local machine.

By installing, you are agreeing to the current Google Terms of Service: https://policies.google.com/terms

and Chrome and Chrome OS Additional Terms of Service: https://www.google.com/chrome/terms/

Portableapps.com is not affiliated with Google. Google and Chrome are trademarks or registered trademarks of Google Inc.

Portions of Google Chrome Portable utilize SQLite3.  The license is available here: https://www.sqlite.org/copyright.html
