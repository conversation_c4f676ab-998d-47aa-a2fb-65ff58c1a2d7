#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome更新功能测试程序
测试Chrome Portable自动下载更新功能

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import tkinter as tk
from tkinter import messagebox
import sys
import threading

# 导入核心模块
try:
    from Chrome更新管理器 import chrome_updater
    from Chrome更新对话框 import ChromeUpdateDialog
    from 主题管理器 import theme_manager, apply_theme
except ImportError as e:
    print(f"❌ 导入核心模块失败: {e}")
    sys.exit(1)

class ChromeUpdateTestApp:
    """Chrome更新功能测试应用"""
    
    def __init__(self):
        """初始化测试应用"""
        self.root = tk.Tk()
        self.setup_window()
        self.setup_ui()
    
    def setup_window(self):
        """设置窗口"""
        self.root.title("🔄 Chrome更新功能测试")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
    
    def setup_ui(self):
        """设置界面"""
        # 主容器
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, text="🔄 Chrome更新功能测试", 
                              font=("Microsoft YaHei", 18, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 功能说明
        info_text = """
Chrome Portable自动下载更新功能测试

主要功能：
• 检测当前Chrome版本
• 获取最新版本信息
• 下载最新Chrome Portable
• 自动备份和安装
• 完整的更新流程管理

测试项目：
• 版本检测功能
• 版本比较功能
• 下载链接获取
• 更新对话框界面
• 完整更新流程
        """
        
        info_label = tk.Label(main_frame, text=info_text.strip(),
                             font=("Microsoft YaHei", 11), justify=tk.LEFT)
        info_label.pack(pady=(0, 20))
        
        # 当前状态显示
        status_frame = tk.LabelFrame(main_frame, text="当前状态", 
                                   font=("Microsoft YaHei", 12))
        status_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.current_version_label = tk.Label(status_frame, text="当前版本: 检测中...", 
                                            font=("Microsoft YaHei", 11))
        self.current_version_label.pack(anchor=tk.W, padx=15, pady=5)
        
        self.latest_version_label = tk.Label(status_frame, text="最新版本: 检测中...", 
                                           font=("Microsoft YaHei", 11))
        self.latest_version_label.pack(anchor=tk.W, padx=15, pady=5)
        
        self.update_status_label = tk.Label(status_frame, text="更新状态: 检查中...", 
                                          font=("Microsoft YaHei", 11))
        self.update_status_label.pack(anchor=tk.W, padx=15, pady=5)
        
        # 测试按钮区域
        test_frame = tk.LabelFrame(main_frame, text="功能测试", 
                                 font=("Microsoft YaHei", 12))
        test_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 第一行按钮
        row1 = tk.Frame(test_frame)
        row1.pack(fill=tk.X, padx=15, pady=10)
        
        self.check_version_btn = tk.Button(row1, text="🔍 检测版本", 
                                         command=self.test_version_check, width=15)
        self.check_version_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.compare_version_btn = tk.Button(row1, text="⚖️ 比较版本", 
                                           command=self.test_version_compare, width=15)
        self.compare_version_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.get_url_btn = tk.Button(row1, text="🔗 获取下载链接", 
                                   command=self.test_download_url, width=15)
        self.get_url_btn.pack(side=tk.LEFT)
        
        # 第二行按钮
        row2 = tk.Frame(test_frame)
        row2.pack(fill=tk.X, padx=15, pady=(0, 10))
        
        self.update_dialog_btn = tk.Button(row2, text="🔄 更新对话框", 
                                         command=self.test_update_dialog, width=15)
        self.update_dialog_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.full_update_btn = tk.Button(row2, text="🚀 完整更新流程", 
                                       command=self.test_full_update, width=15)
        self.full_update_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.refresh_btn = tk.Button(row2, text="🔄 刷新状态", 
                                   command=self.refresh_status, width=15)
        self.refresh_btn.pack(side=tk.LEFT)
        
        # 结果显示
        result_frame = tk.LabelFrame(main_frame, text="测试结果", 
                                   font=("Microsoft YaHei", 12))
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本框和滚动条
        text_frame = tk.Frame(result_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.result_text = tk.Text(text_frame, wrap=tk.WORD, 
                                 font=("Consolas", 9))
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, 
                               command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 应用主题
        theme_manager.apply_theme_to_window(self.root)
        
        # 应用主题到所有组件
        widgets_to_theme = [
            (main_frame, "frame"),
            (title_label, "label"),
            (info_label, "label"),
            (status_frame, "frame"),
            (self.current_version_label, "label"),
            (self.latest_version_label, "label"),
            (self.update_status_label, "label"),
            (test_frame, "frame"),
            (row1, "frame"),
            (row2, "frame"),
            (self.check_version_btn, "button"),
            (self.compare_version_btn, "button"),
            (self.get_url_btn, "button"),
            (self.update_dialog_btn, "button"),
            (self.full_update_btn, "button"),
            (self.refresh_btn, "button"),
            (result_frame, "frame"),
            (text_frame, "frame"),
            (self.result_text, "text")
        ]
        
        for widget, widget_type in widgets_to_theme:
            apply_theme(widget, widget_type)
        
        # 初始化检查
        self.refresh_status()
    
    def log_message(self, message: str):
        """记录消息到结果显示区"""
        self.result_text.insert(tk.END, f"{message}\n")
        self.result_text.see(tk.END)
        self.root.update()
    
    def refresh_status(self):
        """刷新状态显示"""
        def check_in_thread():
            try:
                self.log_message("🔄 刷新状态信息...")
                
                # 获取当前版本
                current = chrome_updater.get_current_version()
                if current:
                    self.current_version_label.config(text=f"当前版本: {current}")
                    self.log_message(f"✅ 当前版本: {current}")
                else:
                    self.current_version_label.config(text="当前版本: 未检测到")
                    self.log_message("❌ 未检测到Chrome Portable")
                
                # 获取最新版本
                latest = chrome_updater.check_latest_version()
                if latest:
                    self.latest_version_label.config(text=f"最新版本: {latest}")
                    self.log_message(f"✅ 最新版本: {latest}")
                else:
                    self.latest_version_label.config(text="最新版本: 检测失败")
                    self.log_message("❌ 无法获取最新版本信息")
                
                # 判断更新状态
                if current and latest:
                    need_update = chrome_updater.compare_versions(current, latest) == -1
                    if need_update:
                        self.update_status_label.config(text="更新状态: 有新版本可用")
                        self.log_message("🆕 发现新版本，可以更新")
                    else:
                        self.update_status_label.config(text="更新状态: 已是最新版本")
                        self.log_message("✅ Chrome已是最新版本")
                else:
                    self.update_status_label.config(text="更新状态: 检查失败")
                
            except Exception as e:
                self.log_message(f"❌ 状态刷新失败: {e}")
        
        # 在后台线程中执行
        threading.Thread(target=check_in_thread, daemon=True).start()
    
    def test_version_check(self):
        """测试版本检测功能"""
        self.log_message("🔍 测试版本检测功能...")
        
        def test_in_thread():
            try:
                current = chrome_updater.get_current_version()
                latest = chrome_updater.check_latest_version()
                
                self.log_message(f"当前版本检测结果: {current or '失败'}")
                self.log_message(f"最新版本检测结果: {latest or '失败'}")
                
                if current and latest:
                    self.log_message("✅ 版本检测功能正常")
                else:
                    self.log_message("❌ 版本检测功能异常")
                    
            except Exception as e:
                self.log_message(f"❌ 版本检测测试失败: {e}")
        
        threading.Thread(target=test_in_thread, daemon=True).start()
    
    def test_version_compare(self):
        """测试版本比较功能"""
        self.log_message("⚖️ 测试版本比较功能...")
        
        # 测试用例
        test_cases = [
            ("120.0.6099.109", "120.0.6099.110", -1),
            ("120.0.6099.110", "120.0.6099.110", 0),
            ("120.0.6099.111", "120.0.6099.110", 1),
            ("119.0.6045.199", "120.0.6099.109", -1),
        ]
        
        for current, latest, expected in test_cases:
            result = chrome_updater.compare_versions(current, latest)
            status = "✅" if result == expected else "❌"
            self.log_message(f"{status} {current} vs {latest}: {result} (期望: {expected})")
        
        self.log_message("⚖️ 版本比较功能测试完成")
    
    def test_download_url(self):
        """测试下载链接获取"""
        self.log_message("🔗 测试下载链接获取...")
        
        def test_in_thread():
            try:
                url = chrome_updater.get_download_url()
                if url:
                    self.log_message(f"✅ 下载链接: {url}")
                else:
                    self.log_message("❌ 无法获取下载链接")
                    
            except Exception as e:
                self.log_message(f"❌ 下载链接获取失败: {e}")
        
        threading.Thread(target=test_in_thread, daemon=True).start()
    
    def test_update_dialog(self):
        """测试更新对话框"""
        self.log_message("🔄 打开Chrome更新对话框...")
        
        try:
            def on_update_complete():
                self.log_message("✅ 更新对话框测试完成")
            
            dialog = ChromeUpdateDialog(self.root, on_update_complete)
            
        except Exception as e:
            self.log_message(f"❌ 更新对话框测试失败: {e}")
            messagebox.showerror("错误", f"打开更新对话框失败: {e}")
    
    def test_full_update(self):
        """测试完整更新流程"""
        result = messagebox.askyesno("确认测试", 
                                   "这将测试完整的Chrome更新流程。\n"
                                   "注意：这可能会实际下载和安装Chrome。\n\n"
                                   "确定要继续吗？")
        if not result:
            return
        
        self.log_message("🚀 开始完整更新流程测试...")
        
        def test_in_thread():
            try:
                def progress_callback(progress, message):
                    self.log_message(f"[{progress}%] {message}")
                
                success = chrome_updater.update_chrome(progress_callback)
                
                if success:
                    self.log_message("✅ 完整更新流程测试成功")
                else:
                    self.log_message("❌ 完整更新流程测试失败")
                    
            except Exception as e:
                self.log_message(f"❌ 完整更新流程测试失败: {e}")
        
        threading.Thread(target=test_in_thread, daemon=True).start()
    
    def run(self):
        """运行测试应用"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🔄 启动Chrome更新功能测试...")
    try:
        app = ChromeUpdateTestApp()
        app.run()
    except Exception as e:
        print(f"❌ 测试应用启动失败: {e}")
        input("按回车键退出...")
